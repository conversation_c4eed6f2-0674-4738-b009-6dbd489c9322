#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖分析脚本
分析项目实际使用的依赖，帮助优化打包
"""

import ast
import os
import sys
from pathlib import Path
from collections import defaultdict

class ImportAnalyzer(ast.NodeVisitor):
    """AST访问器，用于分析import语句"""
    
    def __init__(self):
        self.imports = set()
        self.from_imports = defaultdict(set)
    
    def visit_Import(self, node):
        for alias in node.names:
            self.imports.add(alias.name.split('.')[0])
    
    def visit_ImportFrom(self, node):
        if node.module:
            module = node.module.split('.')[0]
            self.imports.add(module)
            for alias in node.names:
                self.from_imports[module].add(alias.name)

def analyze_file(file_path):
    """分析单个Python文件的导入"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        analyzer = ImportAnalyzer()
        analyzer.visit(tree)
        
        return analyzer.imports, analyzer.from_imports
    except Exception as e:
        print(f"分析文件 {file_path} 时出错: {e}")
        return set(), defaultdict(set)

def analyze_project():
    """分析整个项目的依赖"""
    print("正在分析项目依赖...")
    
    all_imports = set()
    all_from_imports = defaultdict(set)
    
    # 分析所有Python文件
    python_files = list(Path('.').glob('*.py'))
    
    for py_file in python_files:
        if py_file.name.startswith('build_') or py_file.name.startswith('analyze_'):
            continue
            
        print(f"分析文件: {py_file}")
        imports, from_imports = analyze_file(py_file)
        
        all_imports.update(imports)
        for module, items in from_imports.items():
            all_from_imports[module].update(items)
    
    return all_imports, all_from_imports

def categorize_imports(imports):
    """将导入分类"""
    stdlib_modules = {
        'os', 'sys', 'time', 'datetime', 'json', 'logging', 'threading',
        'struct', 're', 'collections', 'pathlib', 'subprocess', 'shutil',
        'ast', 'email', 'html', 'http', 'urllib', 'xml', 'xmlrpc',
        'tkinter', 'unittest', 'doctest', 'pydoc'
    }
    
    large_modules = {
        'PyQt5', 'numpy', 'pandas', 'matplotlib', 'scipy', 'sklearn',
        'tensorflow', 'torch', 'PIL', 'cv2', 'jupyter', 'IPython'
    }
    
    project_modules = set()
    third_party_modules = set()
    stdlib_used = set()
    large_used = set()
    
    for module in imports:
        if module in stdlib_modules:
            stdlib_used.add(module)
        elif module in large_modules:
            large_used.add(module)
        elif module in ['plc', 'serialsetting', 'voltage', 'uitest', 'mainform', 
                       'pressure', 'relay', 'temperature', 'SENT', 'xlsxfile', 
                       'scan', 'log_exception', 'logger']:
            project_modules.add(module)
        else:
            third_party_modules.add(module)
    
    return {
        'stdlib': stdlib_used,
        'large': large_used,
        'project': project_modules,
        'third_party': third_party_modules
    }

def suggest_optimizations(categorized_imports):
    """建议优化方案"""
    print("\n=== 优化建议 ===")
    
    # 检查大型库
    large_libs = categorized_imports['large']
    if large_libs:
        print("⚠️  发现大型库依赖:")
        for lib in large_libs:
            print(f"   - {lib}")
        print("   建议: 确认是否真的需要这些库")
    
    # 检查PyQt5模块使用情况
    third_party = categorized_imports['third_party']
    if 'PyQt5' in third_party:
        print("\n📦 PyQt5 优化建议:")
        print("   - 只导入需要的PyQt5模块")
        print("   - 在spec文件中排除不需要的PyQt5组件")
        print("   - 考虑使用PySide2替代（可能更小）")
    
    # 检查其他第三方库
    other_libs = third_party - {'PyQt5', 'qdarkstyle', 'openpyxl', 'serial', 'modbus_tk'}
    if other_libs:
        print(f"\n🔍 其他第三方库: {', '.join(other_libs)}")
        print("   建议: 检查是否都是必需的")

def generate_requirements():
    """生成requirements.txt"""
    known_packages = {
        'PyQt5': 'PyQt5>=5.15.0',
        'qdarkstyle': 'qdarkstyle>=3.0.0',
        'openpyxl': 'openpyxl>=3.0.0',
        'serial': 'pyserial>=3.5',
        'modbus_tk': 'modbus_tk>=1.1.0'
    }
    
    print("\n=== 生成 requirements.txt ===")
    
    _, all_from_imports = analyze_project()
    
    requirements = []
    for module in known_packages:
        if module in all_from_imports or module == 'serial':  # serial导入为pyserial
            requirements.append(known_packages[module])
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        for req in sorted(requirements):
            f.write(req + '\n')
    
    print("已生成 requirements.txt:")
    for req in sorted(requirements):
        print(f"   {req}")

def main():
    """主函数"""
    print("=== 项目依赖分析 ===")
    
    # 分析项目
    all_imports, all_from_imports = analyze_project()
    
    # 分类导入
    categorized = categorize_imports(all_imports)
    
    # 显示结果
    print(f"\n📊 依赖统计:")
    print(f"   标准库: {len(categorized['stdlib'])} 个")
    print(f"   第三方库: {len(categorized['third_party'])} 个")
    print(f"   大型库: {len(categorized['large'])} 个")
    print(f"   项目模块: {len(categorized['project'])} 个")
    
    print(f"\n📋 第三方库列表:")
    for lib in sorted(categorized['third_party']):
        print(f"   - {lib}")
    
    # 优化建议
    suggest_optimizations(categorized)
    
    # 生成requirements.txt
    generate_requirements()

if __name__ == "__main__":
    main()
