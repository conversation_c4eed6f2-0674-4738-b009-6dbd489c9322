{"version": 1, "disable_existing_loggers": false, "formatters": {"simple": {"format": "[%(levelname)s|%(module)s|L%(lineno)d]: %(message)s"}, "for_user": {"format": "%(message)s"}, "detailed": {"format": "[%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s", "datefmt": "%Y-%m-%dT%H:%M:%S%z"}}, "handlers": {"stdout": {"class": "logging.StreamHandler", "level": "DEBUG", "formatter": "simple", "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.RotatingFileHandler", "level": "DEBUG", "formatter": "detailed", "filename": "logs/app_log.txt", "encoding": "utf-8", "maxBytes": 1000000, "backupCount": 100}}, "loggers": {"mainform": {"level": "DEBUG", "handlers": ["stdout", "file"]}}}