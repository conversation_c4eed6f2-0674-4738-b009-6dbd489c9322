# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file 'uitest.ui'
#
# Created by: PyQt5 UI code generator 5.15.7
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.


from PyQt5 import QtCore, QtGui, QtWidgets


class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1380, 830)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.centralwidget.sizePolicy().hasHeightForWidth())
        self.centralwidget.setSizePolicy(sizePolicy)
        self.centralwidget.setObjectName("centralwidget")
        self.scrollArea = QtWidgets.QScrollArea(self.centralwidget)
        self.scrollArea.setGeometry(QtCore.QRect(10, 0, 1361, 771))
        self.scrollArea.setWidgetResizable(True)
        self.scrollArea.setAlignment(QtCore.Qt.AlignLeading|QtCore.Qt.AlignLeft|QtCore.Qt.AlignVCenter)
        self.scrollArea.setObjectName("scrollArea")
        self.scrollAreaWidgetContents = QtWidgets.QWidget()
        self.scrollAreaWidgetContents.setGeometry(QtCore.QRect(0, 0, 1359, 769))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scrollAreaWidgetContents.sizePolicy().hasHeightForWidth())
        self.scrollAreaWidgetContents.setSizePolicy(sizePolicy)
        self.scrollAreaWidgetContents.setMinimumSize(QtCore.QSize(1359, 769))
        self.scrollAreaWidgetContents.setObjectName("scrollAreaWidgetContents")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.scrollAreaWidgetContents)
        self.verticalLayout.setObjectName("verticalLayout")
        self.tabWidget = QtWidgets.QTabWidget(self.scrollAreaWidgetContents)
        self.tabWidget.setEnabled(True)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy)
        self.tabWidget.setMinimumSize(QtCore.QSize(0, 0))
        self.tabWidget.setMaximumSize(QtCore.QSize(16777215, 16777215))
        font = QtGui.QFont()
        font.setFamily("华光细黑_CNKI")
        font.setPointSize(10)
        self.tabWidget.setFont(font)
        self.tabWidget.setObjectName("tabWidget")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.SerialPressure = QtWidgets.QComboBox(self.tab)
        self.SerialPressure.setGeometry(QtCore.QRect(10, 60, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialPressure.sizePolicy().hasHeightForWidth())
        self.SerialPressure.setSizePolicy(sizePolicy)
        self.SerialPressure.setObjectName("SerialPressure")
        self.SerialPressure.addItem("")
        self.SerialPressure.addItem("")
        self.SerialPressure.addItem("")
        self.SerialPressure.addItem("")
        self.SerialPressure.addItem("")
        self.SerialPressure.addItem("")
        self.pushButton = QtWidgets.QPushButton(self.tab)
        self.pushButton.setGeometry(QtCore.QRect(110, 550, 171, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton.sizePolicy().hasHeightForWidth())
        self.pushButton.setSizePolicy(sizePolicy)
        self.pushButton.setObjectName("pushButton")
        self.label = QtWidgets.QLabel(self.tab)
        self.label.setGeometry(QtCore.QRect(10, 40, 111, 21))
        self.label.setObjectName("label")
        self.label_2 = QtWidgets.QLabel(self.tab)
        self.label_2.setGeometry(QtCore.QRect(10, 90, 121, 21))
        self.label_2.setObjectName("label_2")
        self.label_3 = QtWidgets.QLabel(self.tab)
        self.label_3.setGeometry(QtCore.QRect(10, 140, 121, 21))
        self.label_3.setObjectName("label_3")
        self.label_4 = QtWidgets.QLabel(self.tab)
        self.label_4.setGeometry(QtCore.QRect(10, 190, 121, 21))
        self.label_4.setObjectName("label_4")
        self.label_5 = QtWidgets.QLabel(self.tab)
        self.label_5.setGeometry(QtCore.QRect(10, 240, 121, 21))
        self.label_5.setObjectName("label_5")
        self.SerialSENT = QtWidgets.QComboBox(self.tab)
        self.SerialSENT.setGeometry(QtCore.QRect(10, 110, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialSENT.sizePolicy().hasHeightForWidth())
        self.SerialSENT.setSizePolicy(sizePolicy)
        self.SerialSENT.setObjectName("SerialSENT")
        self.SerialSENT.addItem("")
        self.SerialSENT.addItem("")
        self.SerialSENT.addItem("")
        self.SerialSENT.addItem("")
        self.SerialSENT.addItem("")
        self.SerialSENT.addItem("")
        self.SerialVol = QtWidgets.QComboBox(self.tab)
        self.SerialVol.setGeometry(QtCore.QRect(10, 160, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialVol.sizePolicy().hasHeightForWidth())
        self.SerialVol.setSizePolicy(sizePolicy)
        self.SerialVol.setObjectName("SerialVol")
        self.SerialVol.addItem("")
        self.SerialVol.addItem("")
        self.SerialVol.addItem("")
        self.SerialVol.addItem("")
        self.SerialVol.addItem("")
        self.SerialVol.addItem("")
        self.Serial8left = QtWidgets.QComboBox(self.tab)
        self.Serial8left.setGeometry(QtCore.QRect(10, 210, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Serial8left.sizePolicy().hasHeightForWidth())
        self.Serial8left.setSizePolicy(sizePolicy)
        self.Serial8left.setObjectName("Serial8left")
        self.Serial8left.addItem("")
        self.Serial8left.addItem("")
        self.Serial8left.addItem("")
        self.Serial8left.addItem("")
        self.Serial8left.addItem("")
        self.Serial8left.addItem("")
        self.Serial8right = QtWidgets.QComboBox(self.tab)
        self.Serial8right.setGeometry(QtCore.QRect(10, 260, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Serial8right.sizePolicy().hasHeightForWidth())
        self.Serial8right.setSizePolicy(sizePolicy)
        self.Serial8right.setObjectName("Serial8right")
        self.Serial8right.addItem("")
        self.Serial8right.addItem("")
        self.Serial8right.addItem("")
        self.Serial8right.addItem("")
        self.Serial8right.addItem("")
        self.Serial8right.addItem("")
        self.label_7 = QtWidgets.QLabel(self.tab)
        self.label_7.setGeometry(QtCore.QRect(400, 450, 91, 21))
        self.label_7.setObjectName("label_7")
        self.TempLeft = QtWidgets.QDoubleSpinBox(self.tab)
        self.TempLeft.setGeometry(QtCore.QRect(530, 450, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.TempLeft.sizePolicy().hasHeightForWidth())
        self.TempLeft.setSizePolicy(sizePolicy)
        self.TempLeft.setDecimals(1)
        self.TempLeft.setMinimum(-999.0)
        self.TempLeft.setMaximum(1000.0)
        self.TempLeft.setObjectName("TempLeft")
        self.label_8 = QtWidgets.QLabel(self.tab)
        self.label_8.setGeometry(QtCore.QRect(10, 290, 121, 21))
        self.label_8.setObjectName("label_8")
        self.Serial2 = QtWidgets.QComboBox(self.tab)
        self.Serial2.setGeometry(QtCore.QRect(10, 310, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.Serial2.sizePolicy().hasHeightForWidth())
        self.Serial2.setSizePolicy(sizePolicy)
        self.Serial2.setObjectName("Serial2")
        self.Serial2.addItem("")
        self.Serial2.addItem("")
        self.Serial2.addItem("")
        self.Serial2.addItem("")
        self.Serial2.addItem("")
        self.Serial2.addItem("")
        self.label_19 = QtWidgets.QLabel(self.tab)
        self.label_19.setGeometry(QtCore.QRect(1110, 20, 111, 21))
        self.label_19.setObjectName("label_19")
        self.Model = QtWidgets.QComboBox(self.tab)
        self.Model.setGeometry(QtCore.QRect(1190, 20, 131, 22))
        self.Model.setObjectName("Model")
        self.Model.addItem("")
        self.Model.addItem("")
        self.label_21 = QtWidgets.QLabel(self.tab)
        self.label_21.setGeometry(QtCore.QRect(10, 340, 121, 21))
        self.label_21.setObjectName("label_21")
        self.SerialTempLeft = QtWidgets.QComboBox(self.tab)
        self.SerialTempLeft.setGeometry(QtCore.QRect(10, 360, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialTempLeft.sizePolicy().hasHeightForWidth())
        self.SerialTempLeft.setSizePolicy(sizePolicy)
        self.SerialTempLeft.setObjectName("SerialTempLeft")
        self.SerialTempLeft.addItem("")
        self.SerialTempLeft.addItem("")
        self.SerialTempLeft.addItem("")
        self.SerialTempLeft.addItem("")
        self.SerialTempLeft.addItem("")
        self.SerialTempLeft.addItem("")
        self.label_22 = QtWidgets.QLabel(self.tab)
        self.label_22.setGeometry(QtCore.QRect(10, 440, 121, 21))
        self.label_22.setObjectName("label_22")
        self.SerialScan = QtWidgets.QComboBox(self.tab)
        self.SerialScan.setGeometry(QtCore.QRect(10, 460, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialScan.sizePolicy().hasHeightForWidth())
        self.SerialScan.setSizePolicy(sizePolicy)
        self.SerialScan.setObjectName("SerialScan")
        self.SerialScan.addItem("")
        self.SerialScan.addItem("")
        self.SerialScan.addItem("")
        self.SerialScan.addItem("")
        self.SerialScan.addItem("")
        self.SerialScan.addItem("")
        self.spinBox_3 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_3.setGeometry(QtCore.QRect(270, 60, 91, 21))
        self.spinBox_3.setFocusPolicy(QtCore.Qt.WheelFocus)
        self.spinBox_3.setMaximum(999999999)
        self.spinBox_3.setProperty("value", 9600)
        self.spinBox_3.setObjectName("spinBox_3")
        self.spinBox_4 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_4.setGeometry(QtCore.QRect(270, 110, 91, 21))
        self.spinBox_4.setMaximum(999999999)
        self.spinBox_4.setProperty("value", 115200)
        self.spinBox_4.setObjectName("spinBox_4")
        self.spinBox_5 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_5.setGeometry(QtCore.QRect(270, 160, 91, 21))
        self.spinBox_5.setMaximum(999999999)
        self.spinBox_5.setProperty("value", 9600)
        self.spinBox_5.setObjectName("spinBox_5")
        self.spinBox_6 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_6.setGeometry(QtCore.QRect(270, 210, 91, 21))
        self.spinBox_6.setMaximum(999999999)
        self.spinBox_6.setProperty("value", 9600)
        self.spinBox_6.setObjectName("spinBox_6")
        self.spinBox_7 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_7.setGeometry(QtCore.QRect(270, 260, 91, 21))
        self.spinBox_7.setMaximum(999999999)
        self.spinBox_7.setProperty("value", 9600)
        self.spinBox_7.setObjectName("spinBox_7")
        self.spinBox_8 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_8.setGeometry(QtCore.QRect(270, 310, 91, 21))
        self.spinBox_8.setMaximum(999999999)
        self.spinBox_8.setProperty("value", 9600)
        self.spinBox_8.setObjectName("spinBox_8")
        self.spinBox_9 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_9.setGeometry(QtCore.QRect(270, 360, 91, 21))
        self.spinBox_9.setMaximum(999999999)
        self.spinBox_9.setProperty("value", 9600)
        self.spinBox_9.setObjectName("spinBox_9")
        self.spinBox_10 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_10.setGeometry(QtCore.QRect(270, 410, 91, 21))
        self.spinBox_10.setMaximum(999999999)
        self.spinBox_10.setProperty("value", 9600)
        self.spinBox_10.setObjectName("spinBox_10")
        self.doubleSpinBox_3 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_3.setGeometry(QtCore.QRect(550, 140, 101, 22))
        self.doubleSpinBox_3.setDecimals(5)
        self.doubleSpinBox_3.setMinimum(-999.0)
        self.doubleSpinBox_3.setMaximum(999.0)
        self.doubleSpinBox_3.setProperty("value", 0.6905)
        self.doubleSpinBox_3.setObjectName("doubleSpinBox_3")
        self.doubleSpinBox_4 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_4.setGeometry(QtCore.QRect(840, 140, 101, 22))
        self.doubleSpinBox_4.setDecimals(5)
        self.doubleSpinBox_4.setMinimum(-999.0)
        self.doubleSpinBox_4.setMaximum(999.99)
        self.doubleSpinBox_4.setProperty("value", 0.1105)
        self.doubleSpinBox_4.setObjectName("doubleSpinBox_4")
        self.label_23 = QtWidgets.QLabel(self.tab)
        self.label_23.setGeometry(QtCore.QRect(400, 140, 141, 31))
        self.label_23.setObjectName("label_23")
        self.label_24 = QtWidgets.QLabel(self.tab)
        self.label_24.setGeometry(QtCore.QRect(690, 140, 141, 31))
        self.label_24.setObjectName("label_24")
        self.doubleSpinBox_5 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_5.setGeometry(QtCore.QRect(530, 490, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_5.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_5.setSizePolicy(sizePolicy)
        self.doubleSpinBox_5.setPrefix("")
        self.doubleSpinBox_5.setMinimum(-100.0)
        self.doubleSpinBox_5.setMaximum(0.0)
        self.doubleSpinBox_5.setSingleStep(0.1)
        self.doubleSpinBox_5.setProperty("value", -1.5)
        self.doubleSpinBox_5.setObjectName("doubleSpinBox_5")
        self.label_25 = QtWidgets.QLabel(self.tab)
        self.label_25.setGeometry(QtCore.QRect(400, 490, 121, 21))
        self.label_25.setObjectName("label_25")
        self.doubleSpinBox_6 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_6.setGeometry(QtCore.QRect(530, 530, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_6.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_6.setSizePolicy(sizePolicy)
        self.doubleSpinBox_6.setPrefix("")
        self.doubleSpinBox_6.setMinimum(-100.0)
        self.doubleSpinBox_6.setMaximum(0.0)
        self.doubleSpinBox_6.setSingleStep(0.1)
        self.doubleSpinBox_6.setProperty("value", -1.0)
        self.doubleSpinBox_6.setObjectName("doubleSpinBox_6")
        self.label_26 = QtWidgets.QLabel(self.tab)
        self.label_26.setGeometry(QtCore.QRect(400, 530, 121, 21))
        self.label_26.setObjectName("label_26")
        self.doubleSpinBox_7 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_7.setGeometry(QtCore.QRect(530, 570, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_7.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_7.setSizePolicy(sizePolicy)
        self.doubleSpinBox_7.setPrefix("")
        self.doubleSpinBox_7.setMinimum(-100.0)
        self.doubleSpinBox_7.setMaximum(0.0)
        self.doubleSpinBox_7.setSingleStep(0.1)
        self.doubleSpinBox_7.setProperty("value", -1.5)
        self.doubleSpinBox_7.setObjectName("doubleSpinBox_7")
        self.label_27 = QtWidgets.QLabel(self.tab)
        self.label_27.setGeometry(QtCore.QRect(400, 570, 121, 21))
        self.label_27.setObjectName("label_27")
        self.doubleSpinBox_2 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_2.setGeometry(QtCore.QRect(980, 470, 101, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_2.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_2.setSizePolicy(sizePolicy)
        self.doubleSpinBox_2.setDecimals(1)
        self.doubleSpinBox_2.setProperty("value", 1.0)
        self.doubleSpinBox_2.setObjectName("doubleSpinBox_2")
        self.label_12 = QtWidgets.QLabel(self.tab)
        self.label_12.setGeometry(QtCore.QRect(870, 470, 101, 21))
        self.label_12.setObjectName("label_12")
        self.doubleSpinBox_8 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_8.setGeometry(QtCore.QRect(980, 520, 101, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_8.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_8.setSizePolicy(sizePolicy)
        self.doubleSpinBox_8.setDecimals(1)
        self.doubleSpinBox_8.setMinimum(-100.0)
        self.doubleSpinBox_8.setMaximum(200.0)
        self.doubleSpinBox_8.setObjectName("doubleSpinBox_8")
        self.label_28 = QtWidgets.QLabel(self.tab)
        self.label_28.setGeometry(QtCore.QRect(870, 520, 111, 21))
        self.label_28.setObjectName("label_28")
        self.label_29 = QtWidgets.QLabel(self.tab)
        self.label_29.setGeometry(QtCore.QRect(870, 570, 111, 21))
        self.label_29.setObjectName("label_29")
        self.doubleSpinBox_9 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_9.setGeometry(QtCore.QRect(980, 570, 101, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_9.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_9.setSizePolicy(sizePolicy)
        self.doubleSpinBox_9.setSuffix(" °C")
        self.doubleSpinBox_9.setDecimals(1)
        self.doubleSpinBox_9.setMinimum(-100.0)
        self.doubleSpinBox_9.setMaximum(1000.0)
        self.doubleSpinBox_9.setProperty("value", 80.0)
        self.doubleSpinBox_9.setObjectName("doubleSpinBox_9")
        self.Model_2 = QtWidgets.QComboBox(self.tab)
        self.Model_2.setGeometry(QtCore.QRect(520, 240, 71, 22))
        self.Model_2.setObjectName("Model_2")
        self.Model_2.addItem("")
        self.Model_2.addItem("")
        self.doubleSpinBox_10 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_10.setGeometry(QtCore.QRect(500, 280, 101, 22))
        self.doubleSpinBox_10.setFocusPolicy(QtCore.Qt.ClickFocus)
        self.doubleSpinBox_10.setDecimals(1)
        self.doubleSpinBox_10.setMinimum(-100.0)
        self.doubleSpinBox_10.setMaximum(1000.0)
        self.doubleSpinBox_10.setProperty("value", 70.0)
        self.doubleSpinBox_10.setObjectName("doubleSpinBox_10")
        self.doubleSpinBox_11 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_11.setGeometry(QtCore.QRect(500, 320, 101, 22))
        self.doubleSpinBox_11.setDecimals(1)
        self.doubleSpinBox_11.setMinimum(-100.0)
        self.doubleSpinBox_11.setMaximum(1000.0)
        self.doubleSpinBox_11.setProperty("value", -6.0)
        self.doubleSpinBox_11.setObjectName("doubleSpinBox_11")
        self.label_32 = QtWidgets.QLabel(self.tab)
        self.label_32.setGeometry(QtCore.QRect(610, 280, 121, 31))
        self.label_32.setObjectName("label_32")
        self.doubleSpinBox_12 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_12.setGeometry(QtCore.QRect(740, 280, 81, 22))
        self.doubleSpinBox_12.setDecimals(3)
        self.doubleSpinBox_12.setProperty("value", 4.577)
        self.doubleSpinBox_12.setObjectName("doubleSpinBox_12")
        self.label_33 = QtWidgets.QLabel(self.tab)
        self.label_33.setGeometry(QtCore.QRect(840, 280, 131, 31))
        self.label_33.setObjectName("label_33")
        self.doubleSpinBox_13 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_13.setGeometry(QtCore.QRect(970, 280, 81, 22))
        self.doubleSpinBox_13.setDecimals(3)
        self.doubleSpinBox_13.setProperty("value", 4.673)
        self.doubleSpinBox_13.setObjectName("doubleSpinBox_13")
        self.label_34 = QtWidgets.QLabel(self.tab)
        self.label_34.setGeometry(QtCore.QRect(610, 320, 121, 31))
        self.label_34.setObjectName("label_34")
        self.label_35 = QtWidgets.QLabel(self.tab)
        self.label_35.setGeometry(QtCore.QRect(840, 320, 131, 31))
        self.label_35.setObjectName("label_35")
        self.doubleSpinBox_14 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_14.setGeometry(QtCore.QRect(970, 320, 81, 22))
        self.doubleSpinBox_14.setDecimals(3)
        self.doubleSpinBox_14.setProperty("value", 0.423)
        self.doubleSpinBox_14.setObjectName("doubleSpinBox_14")
        self.doubleSpinBox_15 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_15.setGeometry(QtCore.QRect(740, 320, 81, 22))
        self.doubleSpinBox_15.setDecimals(3)
        self.doubleSpinBox_15.setProperty("value", 0.327)
        self.doubleSpinBox_15.setObjectName("doubleSpinBox_15")
        self.label_36 = QtWidgets.QLabel(self.tab)
        self.label_36.setGeometry(QtCore.QRect(10, 390, 121, 21))
        self.label_36.setObjectName("label_36")
        self.SerialTempRight = QtWidgets.QComboBox(self.tab)
        self.SerialTempRight.setGeometry(QtCore.QRect(10, 410, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialTempRight.sizePolicy().hasHeightForWidth())
        self.SerialTempRight.setSizePolicy(sizePolicy)
        self.SerialTempRight.setObjectName("SerialTempRight")
        self.SerialTempRight.addItem("")
        self.SerialTempRight.addItem("")
        self.SerialTempRight.addItem("")
        self.SerialTempRight.addItem("")
        self.SerialTempRight.addItem("")
        self.SerialTempRight.addItem("")
        self.spinBox_11 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_11.setGeometry(QtCore.QRect(270, 460, 91, 21))
        self.spinBox_11.setMaximum(999999999)
        self.spinBox_11.setProperty("value", 115200)
        self.spinBox_11.setObjectName("spinBox_11")
        self.doubleSpinBox_31 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_31.setGeometry(QtCore.QRect(760, 490, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_31.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_31.setSizePolicy(sizePolicy)
        self.doubleSpinBox_31.setSingleStep(0.1)
        self.doubleSpinBox_31.setProperty("value", 1.5)
        self.doubleSpinBox_31.setObjectName("doubleSpinBox_31")
        self.label_61 = QtWidgets.QLabel(self.tab)
        self.label_61.setGeometry(QtCore.QRect(630, 530, 121, 21))
        self.label_61.setObjectName("label_61")
        self.label_62 = QtWidgets.QLabel(self.tab)
        self.label_62.setGeometry(QtCore.QRect(630, 490, 121, 21))
        self.label_62.setObjectName("label_62")
        self.label_63 = QtWidgets.QLabel(self.tab)
        self.label_63.setGeometry(QtCore.QRect(630, 570, 121, 21))
        self.label_63.setObjectName("label_63")
        self.doubleSpinBox_32 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_32.setGeometry(QtCore.QRect(760, 530, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_32.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_32.setSizePolicy(sizePolicy)
        self.doubleSpinBox_32.setSingleStep(0.1)
        self.doubleSpinBox_32.setProperty("value", 1.0)
        self.doubleSpinBox_32.setObjectName("doubleSpinBox_32")
        self.doubleSpinBox_33 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_33.setGeometry(QtCore.QRect(760, 570, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_33.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_33.setSizePolicy(sizePolicy)
        self.doubleSpinBox_33.setSingleStep(0.1)
        self.doubleSpinBox_33.setProperty("value", 1.5)
        self.doubleSpinBox_33.setObjectName("doubleSpinBox_33")
        self.label_64 = QtWidgets.QLabel(self.tab)
        self.label_64.setGeometry(QtCore.QRect(630, 450, 101, 21))
        self.label_64.setObjectName("label_64")
        self.TempRight = QtWidgets.QDoubleSpinBox(self.tab)
        self.TempRight.setGeometry(QtCore.QRect(760, 450, 91, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.TempRight.sizePolicy().hasHeightForWidth())
        self.TempRight.setSizePolicy(sizePolicy)
        self.TempRight.setDecimals(1)
        self.TempRight.setMinimum(-999.0)
        self.TempRight.setMaximum(1000.0)
        self.TempRight.setObjectName("TempRight")
        self.label_37 = QtWidgets.QLabel(self.tab)
        self.label_37.setGeometry(QtCore.QRect(10, 490, 91, 21))
        self.label_37.setObjectName("label_37")
        self.SerialPLC = QtWidgets.QComboBox(self.tab)
        self.SerialPLC.setGeometry(QtCore.QRect(10, 510, 241, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.SerialPLC.sizePolicy().hasHeightForWidth())
        self.SerialPLC.setSizePolicy(sizePolicy)
        self.SerialPLC.setObjectName("SerialPLC")
        self.SerialPLC.addItem("")
        self.SerialPLC.addItem("")
        self.SerialPLC.addItem("")
        self.SerialPLC.addItem("")
        self.SerialPLC.addItem("")
        self.SerialPLC.addItem("")
        self.spinBox_12 = QtWidgets.QSpinBox(self.tab)
        self.spinBox_12.setGeometry(QtCore.QRect(270, 510, 91, 21))
        self.spinBox_12.setMaximum(999999999)
        self.spinBox_12.setProperty("value", 9600)
        self.spinBox_12.setObjectName("spinBox_12")
        self.line = QtWidgets.QFrame(self.tab)
        self.line.setGeometry(QtCore.QRect(370, 10, 20, 581))
        self.line.setFrameShape(QtWidgets.QFrame.VLine)
        self.line.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line.setObjectName("line")
        self.line_2 = QtWidgets.QFrame(self.tab)
        self.line_2.setGeometry(QtCore.QRect(400, 190, 681, 20))
        self.line_2.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_2.setObjectName("line_2")
        self.line_3 = QtWidgets.QFrame(self.tab)
        self.line_3.setGeometry(QtCore.QRect(410, 390, 671, 20))
        self.line_3.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_3.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_3.setObjectName("line_3")
        self.line_4 = QtWidgets.QFrame(self.tab)
        self.line_4.setGeometry(QtCore.QRect(1090, 10, 20, 581))
        self.line_4.setFrameShape(QtWidgets.QFrame.VLine)
        self.line_4.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_4.setObjectName("line_4")
        self.label_10 = QtWidgets.QLabel(self.tab)
        self.label_10.setGeometry(QtCore.QRect(550, 410, 91, 21))
        self.label_10.setObjectName("label_10")
        self.doubleSpinBox_16 = QtWidgets.QDoubleSpinBox(self.tab)
        self.doubleSpinBox_16.setGeometry(QtCore.QRect(660, 410, 81, 22))
        self.doubleSpinBox_16.setDecimals(3)
        self.doubleSpinBox_16.setProperty("value", 0.423)
        self.doubleSpinBox_16.setObjectName("doubleSpinBox_16")
        self.line_5 = QtWidgets.QFrame(self.tab)
        self.line_5.setGeometry(QtCore.QRect(400, 90, 681, 20))
        self.line_5.setFrameShape(QtWidgets.QFrame.HLine)
        self.line_5.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.line_5.setObjectName("line_5")
        self.savedPathButton = QtWidgets.QPushButton(self.tab)
        self.savedPathButton.setGeometry(QtCore.QRect(890, 10, 161, 31))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.savedPathButton.sizePolicy().hasHeightForWidth())
        self.savedPathButton.setSizePolicy(sizePolicy)
        self.savedPathButton.setObjectName("savedPathButton")
        self.savedPath = QtWidgets.QLineEdit(self.tab)
        self.savedPath.setGeometry(QtCore.QRect(400, 20, 481, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.savedPath.sizePolicy().hasHeightForWidth())
        self.savedPath.setSizePolicy(sizePolicy)
        self.savedPath.setAcceptDrops(True)
        self.savedPath.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.savedPath.setReadOnly(True)
        self.savedPath.setObjectName("savedPath")
        self.label_38 = QtWidgets.QLabel(self.tab)
        self.label_38.setGeometry(QtCore.QRect(400, 240, 121, 31))
        self.label_38.setObjectName("label_38")
        self.label_39 = QtWidgets.QLabel(self.tab)
        self.label_39.setGeometry(QtCore.QRect(400, 280, 91, 31))
        self.label_39.setObjectName("label_39")
        self.label_40 = QtWidgets.QLabel(self.tab)
        self.label_40.setGeometry(QtCore.QRect(400, 320, 91, 31))
        self.label_40.setObjectName("label_40")
        self.backupPath = QtWidgets.QLineEdit(self.tab)
        self.backupPath.setGeometry(QtCore.QRect(400, 60, 481, 20))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.backupPath.sizePolicy().hasHeightForWidth())
        self.backupPath.setSizePolicy(sizePolicy)
        self.backupPath.setAcceptDrops(True)
        self.backupPath.setLayoutDirection(QtCore.Qt.LeftToRight)
        self.backupPath.setReadOnly(True)
        self.backupPath.setObjectName("backupPath")
        self.backupPathButton = QtWidgets.QPushButton(self.tab)
        self.backupPathButton.setGeometry(QtCore.QRect(890, 50, 161, 31))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.backupPathButton.sizePolicy().hasHeightForWidth())
        self.backupPathButton.setSizePolicy(sizePolicy)
        self.backupPathButton.setObjectName("backupPathButton")
        self.tabWidget_3 = QtWidgets.QTabWidget(self.tab)
        self.tabWidget_3.setGeometry(QtCore.QRect(1100, 50, 231, 551))
        self.tabWidget_3.setObjectName("tabWidget_3")
        self.tab_6 = QtWidgets.QWidget()
        self.tab_6.setObjectName("tab_6")
        self.label_6 = QtWidgets.QLabel(self.tab_6)
        self.label_6.setGeometry(QtCore.QRect(10, 0, 81, 21))
        self.label_6.setObjectName("label_6")
        self.tableWidget_2 = QtWidgets.QTableWidget(self.tab_6)
        self.tableWidget_2.setGeometry(QtCore.QRect(10, 30, 211, 481))
        self.tableWidget_2.setFrameShadow(QtWidgets.QFrame.Sunken)
        self.tableWidget_2.setAlternatingRowColors(True)
        self.tableWidget_2.setSelectionMode(QtWidgets.QAbstractItemView.NoSelection)
        self.tableWidget_2.setGridStyle(QtCore.Qt.DotLine)
        self.tableWidget_2.setObjectName("tableWidget_2")
        self.tableWidget_2.setColumnCount(1)
        self.tableWidget_2.setRowCount(10)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(0, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(1, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(2, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(3, 0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_2.setItem(4, 0, item)
        self.tableWidget_2.horizontalHeader().setCascadingSectionResizes(False)
        self.tableWidget_2.horizontalHeader().setHighlightSections(True)
        self.tableWidget_2.horizontalHeader().setSortIndicatorShown(False)
        self.tableWidget_2.horizontalHeader().setStretchLastSection(True)
        self.tableWidget_2.verticalHeader().setCascadingSectionResizes(False)
        self.tableWidget_2.verticalHeader().setSortIndicatorShown(False)
        self.tableWidget_2.verticalHeader().setStretchLastSection(False)
        self.TestPoint = QtWidgets.QSpinBox(self.tab_6)
        self.TestPoint.setGeometry(QtCore.QRect(110, 0, 71, 22))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.TestPoint.sizePolicy().hasHeightForWidth())
        self.TestPoint.setSizePolicy(sizePolicy)
        self.TestPoint.setProperty("value", 5)
        self.TestPoint.setObjectName("TestPoint")
        self.tabWidget_3.addTab(self.tab_6, "")
        self.tab_7 = QtWidgets.QWidget()
        self.tab_7.setObjectName("tab_7")
        self.Low_Pressure_Value = QtWidgets.QDoubleSpinBox(self.tab_7)
        self.Low_Pressure_Value.setGeometry(QtCore.QRect(90, 30, 101, 21))
        self.Low_Pressure_Value.setMinimum(-9999.0)
        self.Low_Pressure_Value.setMaximum(9999.0)
        self.Low_Pressure_Value.setSingleStep(1.0)
        self.Low_Pressure_Value.setObjectName("Low_Pressure_Value")
        self.High_Pressure_Value = QtWidgets.QDoubleSpinBox(self.tab_7)
        self.High_Pressure_Value.setGeometry(QtCore.QRect(90, 70, 101, 21))
        self.High_Pressure_Value.setMinimum(-9999.0)
        self.High_Pressure_Value.setMaximum(9999.0)
        self.High_Pressure_Value.setObjectName("High_Pressure_Value")
        self.Interval_Pressure = QtWidgets.QDoubleSpinBox(self.tab_7)
        self.Interval_Pressure.setGeometry(QtCore.QRect(90, 110, 101, 21))
        self.Interval_Pressure.setMinimum(-9999.0)
        self.Interval_Pressure.setMaximum(9999.0)
        self.Interval_Pressure.setProperty("value", 0.0)
        self.Interval_Pressure.setObjectName("Interval_Pressure")
        self.label_11 = QtWidgets.QLabel(self.tab_7)
        self.label_11.setGeometry(QtCore.QRect(10, 30, 71, 16))
        self.label_11.setObjectName("label_11")
        self.label_13 = QtWidgets.QLabel(self.tab_7)
        self.label_13.setGeometry(QtCore.QRect(10, 70, 71, 16))
        self.label_13.setObjectName("label_13")
        self.label_14 = QtWidgets.QLabel(self.tab_7)
        self.label_14.setGeometry(QtCore.QRect(10, 110, 71, 16))
        self.label_14.setObjectName("label_14")
        self.tabWidget_3.addTab(self.tab_7, "")
        self.tabWidget_3.raise_()
        self.SerialPressure.raise_()
        self.pushButton.raise_()
        self.label.raise_()
        self.label_2.raise_()
        self.label_3.raise_()
        self.label_4.raise_()
        self.label_5.raise_()
        self.SerialSENT.raise_()
        self.SerialVol.raise_()
        self.Serial8left.raise_()
        self.Serial8right.raise_()
        self.label_7.raise_()
        self.TempLeft.raise_()
        self.label_8.raise_()
        self.Serial2.raise_()
        self.label_19.raise_()
        self.Model.raise_()
        self.label_21.raise_()
        self.SerialTempLeft.raise_()
        self.label_22.raise_()
        self.SerialScan.raise_()
        self.spinBox_3.raise_()
        self.spinBox_4.raise_()
        self.spinBox_5.raise_()
        self.spinBox_6.raise_()
        self.spinBox_7.raise_()
        self.spinBox_8.raise_()
        self.spinBox_9.raise_()
        self.spinBox_10.raise_()
        self.doubleSpinBox_3.raise_()
        self.doubleSpinBox_4.raise_()
        self.label_23.raise_()
        self.label_24.raise_()
        self.doubleSpinBox_5.raise_()
        self.label_25.raise_()
        self.doubleSpinBox_6.raise_()
        self.label_26.raise_()
        self.doubleSpinBox_7.raise_()
        self.label_27.raise_()
        self.doubleSpinBox_2.raise_()
        self.label_12.raise_()
        self.doubleSpinBox_8.raise_()
        self.label_28.raise_()
        self.label_29.raise_()
        self.doubleSpinBox_9.raise_()
        self.Model_2.raise_()
        self.doubleSpinBox_10.raise_()
        self.doubleSpinBox_11.raise_()
        self.label_32.raise_()
        self.doubleSpinBox_12.raise_()
        self.label_33.raise_()
        self.doubleSpinBox_13.raise_()
        self.label_34.raise_()
        self.label_35.raise_()
        self.doubleSpinBox_14.raise_()
        self.doubleSpinBox_15.raise_()
        self.label_36.raise_()
        self.SerialTempRight.raise_()
        self.spinBox_11.raise_()
        self.doubleSpinBox_31.raise_()
        self.label_61.raise_()
        self.label_62.raise_()
        self.label_63.raise_()
        self.doubleSpinBox_32.raise_()
        self.doubleSpinBox_33.raise_()
        self.label_64.raise_()
        self.TempRight.raise_()
        self.label_37.raise_()
        self.SerialPLC.raise_()
        self.spinBox_12.raise_()
        self.line.raise_()
        self.line_2.raise_()
        self.line_3.raise_()
        self.line_4.raise_()
        self.label_10.raise_()
        self.doubleSpinBox_16.raise_()
        self.line_5.raise_()
        self.savedPathButton.raise_()
        self.savedPath.raise_()
        self.label_38.raise_()
        self.label_39.raise_()
        self.label_40.raise_()
        self.backupPath.raise_()
        self.backupPathButton.raise_()
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.pushButton_2 = QtWidgets.QPushButton(self.tab_2)
        self.pushButton_2.setGeometry(QtCore.QRect(1170, 90, 141, 51))
        self.pushButton_2.setObjectName("pushButton_2")
        self.pushButton_5 = QtWidgets.QPushButton(self.tab_2)
        self.pushButton_5.setGeometry(QtCore.QRect(1170, 200, 141, 51))
        self.pushButton_5.setObjectName("pushButton_5")
        self.testStatusGroup = QtWidgets.QGroupBox(self.tab_2)
        self.testStatusGroup.setGeometry(QtCore.QRect(1150, 280, 181, 160))
        self.testStatusGroup.setObjectName("testStatusGroup")
        self.testStatusLabel = QtWidgets.QLabel(self.testStatusGroup)
        self.testStatusLabel.setGeometry(QtCore.QRect(10, 25, 161, 30))
        self.testStatusLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.testStatusLabel.setObjectName("testStatusLabel")
        self.testTimeLabel = QtWidgets.QLabel(self.testStatusGroup)
        self.testTimeLabel.setGeometry(QtCore.QRect(10, 65, 161, 30))
        self.testTimeLabel.setAlignment(QtCore.Qt.AlignCenter)
        self.testTimeLabel.setObjectName("testTimeLabel")
        self.terminateButton = QtWidgets.QPushButton(self.testStatusGroup)
        self.terminateButton.setGeometry(QtCore.QRect(10, 105, 161, 40))
        self.terminateButton.setObjectName("terminateButton")
        self.tabWidget_2 = QtWidgets.QTabWidget(self.tab_2)
        self.tabWidget_2.setGeometry(QtCore.QRect(30, 0, 1101, 601))
        self.tabWidget_2.setObjectName("tabWidget_2")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.tableWidgetLeft = QtWidgets.QTableWidget(self.tab_4)
        self.tableWidgetLeft.setGeometry(QtCore.QRect(10, 10, 1071, 551))
        self.tableWidgetLeft.setLineWidth(2)
        self.tableWidgetLeft.setObjectName("tableWidgetLeft")
        self.tableWidgetLeft.setColumnCount(9)
        self.tableWidgetLeft.setRowCount(25)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(19, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(20, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(21, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(22, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(23, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setVerticalHeaderItem(24, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setItem(5, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setItem(5, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetLeft.setItem(5, 3, item)
        self.tableWidgetLeft.horizontalHeader().setCascadingSectionResizes(True)
        self.tableWidgetLeft.horizontalHeader().setDefaultSectionSize(100)
        self.tableWidgetLeft.verticalHeader().setDefaultSectionSize(40)
        self.tableWidgetLeft.verticalHeader().setMinimumSectionSize(18)
        self.tabWidget_2.addTab(self.tab_4, "")
        self.tab_5 = QtWidgets.QWidget()
        self.tab_5.setObjectName("tab_5")
        self.tableWidgetRight = QtWidgets.QTableWidget(self.tab_5)
        self.tableWidgetRight.setGeometry(QtCore.QRect(10, 10, 1071, 551))
        self.tableWidgetRight.setLineWidth(2)
        self.tableWidgetRight.setObjectName("tableWidgetRight")
        self.tableWidgetRight.setColumnCount(9)
        self.tableWidgetRight.setRowCount(25)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(14, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(15, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(16, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(17, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(18, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(19, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(20, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(21, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(22, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(23, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setVerticalHeaderItem(24, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setHorizontalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setItem(0, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setItem(0, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setItem(0, 3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setItem(5, 1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setItem(5, 2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidgetRight.setItem(5, 3, item)
        self.tableWidgetRight.horizontalHeader().setCascadingSectionResizes(True)
        self.tableWidgetRight.horizontalHeader().setDefaultSectionSize(100)
        self.tableWidgetRight.horizontalHeader().setMinimumSectionSize(31)
        self.tableWidgetRight.verticalHeader().setDefaultSectionSize(40)
        self.tableWidgetRight.verticalHeader().setMinimumSectionSize(18)
        self.tabWidget_2.addTab(self.tab_5, "")
        self.ledLabel = QtWidgets.QLabel(self.tab_2)
        self.ledLabel.setGeometry(QtCore.QRect(1200, 470, 21, 16))
        self.ledLabel.setObjectName("ledLabel")
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.label_9 = QtWidgets.QLabel(self.tab_3)
        self.label_9.setGeometry(QtCore.QRect(10, 40, 131, 21))
        self.label_9.setObjectName("label_9")
        self.tableWidget_3 = QtWidgets.QTableWidget(self.tab_3)
        self.tableWidget_3.setGeometry(QtCore.QRect(90, 140, 1001, 441))
        self.tableWidget_3.setObjectName("tableWidget_3")
        self.tableWidget_3.setColumnCount(5)
        self.tableWidget_3.setRowCount(14)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(6, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(7, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(8, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(9, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(10, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(11, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(12, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setVerticalHeaderItem(13, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_3.setHorizontalHeaderItem(4, item)
        self.tableWidget_3.horizontalHeader().setDefaultSectionSize(180)
        self.tableWidget_3.verticalHeader().setDefaultSectionSize(29)
        self.tableWidget_5 = QtWidgets.QTableWidget(self.tab_3)
        self.tableWidget_5.setGeometry(QtCore.QRect(330, 30, 1011, 71))
        self.tableWidget_5.setObjectName("tableWidget_5")
        self.tableWidget_5.setColumnCount(7)
        self.tableWidget_5.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(1, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(2, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(3, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(4, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(5, item)
        item = QtWidgets.QTableWidgetItem()
        self.tableWidget_5.setHorizontalHeaderItem(6, item)
        self.tableWidget_5.horizontalHeader().setDefaultSectionSize(159)
        self.searchTableWidget = QtWidgets.QTableWidget(self.tab_3)
        self.searchTableWidget.setGeometry(QtCore.QRect(140, 30, 181, 41))
        self.searchTableWidget.setObjectName("searchTableWidget")
        self.searchTableWidget.setColumnCount(1)
        self.searchTableWidget.setRowCount(1)
        item = QtWidgets.QTableWidgetItem()
        self.searchTableWidget.setVerticalHeaderItem(0, item)
        item = QtWidgets.QTableWidgetItem()
        self.searchTableWidget.setHorizontalHeaderItem(0, item)
        self.searchTableWidget.horizontalHeader().setVisible(False)
        self.searchTableWidget.horizontalHeader().setDefaultSectionSize(180)
        self.searchTableWidget.verticalHeader().setVisible(False)
        self.searchButton = QtWidgets.QPushButton(self.tab_3)
        self.searchButton.setGeometry(QtCore.QRect(80, 80, 171, 41))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.searchButton.sizePolicy().hasHeightForWidth())
        self.searchButton.setSizePolicy(sizePolicy)
        self.searchButton.setObjectName("searchButton")
        self.tabWidget.addTab(self.tab_3, "")
        self.verticalLayout.addWidget(self.tabWidget)
        self.textBrowser = QtWidgets.QTextBrowser(self.scrollAreaWidgetContents)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.textBrowser.sizePolicy().hasHeightForWidth())
        self.textBrowser.setSizePolicy(sizePolicy)
        self.textBrowser.setMinimumSize(QtCore.QSize(0, 0))
        self.textBrowser.setMaximumSize(QtCore.QSize(13411, 111))
        self.textBrowser.setObjectName("textBrowser")
        self.verticalLayout.addWidget(self.textBrowser)
        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1380, 22))
        self.menubar.setObjectName("menubar")
        self.menu = QtWidgets.QMenu(self.menubar)
        self.menu.setObjectName("menu")
        self.menu_2 = QtWidgets.QMenu(self.menubar)
        self.menu_2.setObjectName("menu_2")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)
        self.loadConfig = QtWidgets.QAction(MainWindow)
        self.loadConfig.setObjectName("loadConfig")
        self.saveConfig = QtWidgets.QAction(MainWindow)
        self.saveConfig.setObjectName("saveConfig")
        self.resPath = QtWidgets.QAction(MainWindow)
        self.resPath.setObjectName("resPath")
        self.actionbk = QtWidgets.QAction(MainWindow)
        self.actionbk.setObjectName("actionbk")
        self.menu.addAction(self.loadConfig)
        self.menu.addAction(self.saveConfig)
        self.menu.addAction(self.actionbk)
        self.menubar.addAction(self.menu.menuAction())
        self.menubar.addAction(self.menu_2.menuAction())

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(2)
        self.tabWidget_3.setCurrentIndex(0)
        self.tabWidget_2.setCurrentIndex(1)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.SerialPressure.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialPressure.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialPressure.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialPressure.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialPressure.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialPressure.setItemText(5, _translate("MainWindow", "COM6"))
        self.pushButton.setText(_translate("MainWindow", "刷新测试串口号"))
        self.label.setText(_translate("MainWindow", "压力源串口号"))
        self.label_2.setText(_translate("MainWindow", "SENT接口串口号"))
        self.label_3.setText(_translate("MainWindow", "电压表串口号"))
        self.label_4.setText(_translate("MainWindow", "左继电器串口号"))
        self.label_5.setText(_translate("MainWindow", "右继电器串口号"))
        self.SerialSENT.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialSENT.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialSENT.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialSENT.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialSENT.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialSENT.setItemText(5, _translate("MainWindow", "COM6"))
        self.SerialVol.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialVol.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialVol.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialVol.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialVol.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialVol.setItemText(5, _translate("MainWindow", "COM6"))
        self.Serial8left.setItemText(0, _translate("MainWindow", "COM1"))
        self.Serial8left.setItemText(1, _translate("MainWindow", "COM2"))
        self.Serial8left.setItemText(2, _translate("MainWindow", "COM3"))
        self.Serial8left.setItemText(3, _translate("MainWindow", "COM4"))
        self.Serial8left.setItemText(4, _translate("MainWindow", "COM5"))
        self.Serial8left.setItemText(5, _translate("MainWindow", "COM6"))
        self.Serial8right.setItemText(0, _translate("MainWindow", "COM1"))
        self.Serial8right.setItemText(1, _translate("MainWindow", "COM2"))
        self.Serial8right.setItemText(2, _translate("MainWindow", "COM3"))
        self.Serial8right.setItemText(3, _translate("MainWindow", "COM4"))
        self.Serial8right.setItemText(4, _translate("MainWindow", "COM5"))
        self.Serial8right.setItemText(5, _translate("MainWindow", "COM6"))
        self.label_7.setText(_translate("MainWindow", "左烘箱温度"))
        self.TempLeft.setSuffix(_translate("MainWindow", " °C"))
        self.label_8.setText(_translate("MainWindow", "测量模式串口号"))
        self.Serial2.setItemText(0, _translate("MainWindow", "COM1"))
        self.Serial2.setItemText(1, _translate("MainWindow", "COM2"))
        self.Serial2.setItemText(2, _translate("MainWindow", "COM3"))
        self.Serial2.setItemText(3, _translate("MainWindow", "COM4"))
        self.Serial2.setItemText(4, _translate("MainWindow", "COM5"))
        self.Serial2.setItemText(5, _translate("MainWindow", "COM6"))
        self.label_19.setText(_translate("MainWindow", "采集模式"))
        self.Model.setItemText(0, _translate("MainWindow", "模拟电压"))
        self.Model.setItemText(1, _translate("MainWindow", "SENT信号"))
        self.label_21.setText(_translate("MainWindow", "左温箱串口号"))
        self.SerialTempLeft.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialTempLeft.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialTempLeft.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialTempLeft.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialTempLeft.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialTempLeft.setItemText(5, _translate("MainWindow", "COM6"))
        self.label_22.setText(_translate("MainWindow", "扫码枪串口号"))
        self.SerialScan.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialScan.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialScan.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialScan.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialScan.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialScan.setItemText(5, _translate("MainWindow", "COM6"))
        self.label_23.setText(_translate("MainWindow", "压力转换公式截距"))
        self.label_24.setText(_translate("MainWindow", "压力转换公式斜率"))
        self.doubleSpinBox_5.setSuffix(_translate("MainWindow", " %"))
        self.label_25.setText(_translate("MainWindow", "低温负误差要求"))
        self.doubleSpinBox_6.setSuffix(_translate("MainWindow", " %"))
        self.label_26.setText(_translate("MainWindow", "中温负误差要求"))
        self.doubleSpinBox_7.setSuffix(_translate("MainWindow", " %"))
        self.label_27.setText(_translate("MainWindow", "高温负误差要求"))
        self.doubleSpinBox_2.setPrefix(_translate("MainWindow", "±"))
        self.doubleSpinBox_2.setSuffix(_translate("MainWindow", " °C"))
        self.label_12.setText(_translate("MainWindow", "温度允许波动范围"))
        self.doubleSpinBox_8.setSuffix(_translate("MainWindow", " °C"))
        self.label_28.setText(_translate("MainWindow", "低温中温界点"))
        self.label_29.setText(_translate("MainWindow", "中温高温界点"))
        self.Model_2.setItemText(0, _translate("MainWindow", "否"))
        self.Model_2.setItemText(1, _translate("MainWindow", "是"))
        self.doubleSpinBox_10.setSuffix(_translate("MainWindow", " kPa"))
        self.doubleSpinBox_11.setSuffix(_translate("MainWindow", " kPa"))
        self.label_32.setText(_translate("MainWindow", "上钳位输出下限"))
        self.label_33.setText(_translate("MainWindow", "上钳位输出上限"))
        self.label_34.setText(_translate("MainWindow", "下钳位输出下限"))
        self.label_35.setText(_translate("MainWindow", "下钳位输出上限"))
        self.label_36.setText(_translate("MainWindow", "右温箱串口号"))
        self.SerialTempRight.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialTempRight.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialTempRight.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialTempRight.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialTempRight.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialTempRight.setItemText(5, _translate("MainWindow", "COM6"))
        self.doubleSpinBox_31.setSuffix(_translate("MainWindow", " %"))
        self.label_61.setText(_translate("MainWindow", "中温正误差要求"))
        self.label_62.setText(_translate("MainWindow", "低温正误差要求"))
        self.label_63.setText(_translate("MainWindow", "高温正误差要求"))
        self.doubleSpinBox_32.setSuffix(_translate("MainWindow", " %"))
        self.doubleSpinBox_33.setSuffix(_translate("MainWindow", " %"))
        self.label_64.setText(_translate("MainWindow", "右烘箱温度"))
        self.TempRight.setSuffix(_translate("MainWindow", " °C"))
        self.label_37.setText(_translate("MainWindow", "PLC串口号"))
        self.SerialPLC.setItemText(0, _translate("MainWindow", "COM1"))
        self.SerialPLC.setItemText(1, _translate("MainWindow", "COM2"))
        self.SerialPLC.setItemText(2, _translate("MainWindow", "COM3"))
        self.SerialPLC.setItemText(3, _translate("MainWindow", "COM4"))
        self.SerialPLC.setItemText(4, _translate("MainWindow", "COM5"))
        self.SerialPLC.setItemText(5, _translate("MainWindow", "COM6"))
        self.label_10.setText(_translate("MainWindow", "误差对比值"))
        self.savedPathButton.setText(_translate("MainWindow", "测试结果保存路径"))
        self.savedPath.setText(_translate("MainWindow", "C:/NCGK/today"))
        self.label_38.setText(_translate("MainWindow", "是否钳位测试"))
        self.label_39.setText(_translate("MainWindow", "上钳位压力"))
        self.label_40.setText(_translate("MainWindow", "下钳位压力"))
        self.backupPath.setText(_translate("MainWindow", "C:/NCGK/today"))
        self.backupPathButton.setText(_translate("MainWindow", "备份数据保存路径"))
        self.label_6.setText(_translate("MainWindow", "点数"))
        item = self.tableWidget_2.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "测试点1"))
        item = self.tableWidget_2.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "测试点2"))
        item = self.tableWidget_2.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "测试点3"))
        item = self.tableWidget_2.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "测试点4"))
        item = self.tableWidget_2.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "测试点5"))
        item = self.tableWidget_2.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "测试点6"))
        item = self.tableWidget_2.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "测试点7"))
        item = self.tableWidget_2.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "测试点8"))
        item = self.tableWidget_2.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "测试点9"))
        item = self.tableWidget_2.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "测试点10"))
        item = self.tableWidget_2.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "压力值(kPa)"))
        __sortingEnabled = self.tableWidget_2.isSortingEnabled()
        self.tableWidget_2.setSortingEnabled(False)
        item = self.tableWidget_2.item(0, 0)
        item.setText(_translate("MainWindow", "10"))
        item = self.tableWidget_2.item(1, 0)
        item.setText(_translate("MainWindow", "20"))
        item = self.tableWidget_2.item(2, 0)
        item.setText(_translate("MainWindow", "30"))
        item = self.tableWidget_2.item(3, 0)
        item.setText(_translate("MainWindow", "40"))
        item = self.tableWidget_2.item(4, 0)
        item.setText(_translate("MainWindow", "50"))
        self.tableWidget_2.setSortingEnabled(__sortingEnabled)
        self.tabWidget_3.setTabText(self.tabWidget_3.indexOf(self.tab_6), _translate("MainWindow", "压力写值模式"))
        self.label_11.setText(_translate("MainWindow", "起始压力值"))
        self.label_13.setText(_translate("MainWindow", "终点压力值"))
        self.label_14.setText(_translate("MainWindow", "间隔压力值"))
        self.tabWidget_3.setTabText(self.tabWidget_3.indexOf(self.tab_7), _translate("MainWindow", "压力间隔模式"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("MainWindow", "设置"))
        self.pushButton_2.setText(_translate("MainWindow", "开始测试"))
        self.pushButton_5.setText(_translate("MainWindow", "清空数据"))
        self.testStatusGroup.setTitle(_translate("MainWindow", "测试状态"))
        self.testStatusLabel.setStyleSheet(_translate("MainWindow", "QLabel {\n"
"  background-color: #f0f0f0;\n"
"  border: 1px solid #ccc;\n"
"  border-radius: 5px;\n"
"  padding: 5px;\n"
"  font-weight: bold;\n"
"}"))
        self.testStatusLabel.setText(_translate("MainWindow", "状态：待机中"))
        self.testTimeLabel.setStyleSheet(_translate("MainWindow", "QLabel {\n"
"  background-color: #e8f4fd;\n"
"  border: 1px solid #4a90e2;\n"
"  border-radius: 5px;\n"
"  padding: 5px;\n"
"  font-family: \'Courier New\', monospace;\n"
"  font-size: 12px;\n"
"  font-weight: bold;\n"
"}"))
        self.testTimeLabel.setText(_translate("MainWindow", "用时：00:00:00"))
        self.terminateButton.setStyleSheet(_translate("MainWindow", "QPushButton {\n"
"  background-color: #dc3545;\n"
"  color: white;\n"
"  border: none;\n"
"  border-radius: 5px;\n"
"  font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"  background-color: #c82333;\n"
"}\n"
"QPushButton:pressed {\n"
"  background-color: #bd2130;\n"
"}"))
        self.terminateButton.setText(_translate("MainWindow", "终止并复位"))
        item = self.tableWidgetLeft.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "序列号"))
        item = self.tableWidgetLeft.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "扫码等级"))
        item = self.tableWidgetLeft.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "是否合格"))
        item = self.tableWidgetLeft.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "上钳位"))
        item = self.tableWidgetLeft.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "下钳位"))
        item = self.tableWidgetLeft.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "测试值1"))
        item = self.tableWidgetLeft.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "误差1"))
        item = self.tableWidgetLeft.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "测试值2"))
        item = self.tableWidgetLeft.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "误差2"))
        item = self.tableWidgetLeft.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "测试值3"))
        item = self.tableWidgetLeft.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "误差3"))
        item = self.tableWidgetLeft.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "测试值4"))
        item = self.tableWidgetLeft.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "误差4"))
        item = self.tableWidgetLeft.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "测试值5"))
        item = self.tableWidgetLeft.verticalHeaderItem(14)
        item.setText(_translate("MainWindow", "误差5"))
        item = self.tableWidgetLeft.verticalHeaderItem(15)
        item.setText(_translate("MainWindow", "测试值6"))
        item = self.tableWidgetLeft.verticalHeaderItem(16)
        item.setText(_translate("MainWindow", "误差6"))
        item = self.tableWidgetLeft.verticalHeaderItem(17)
        item.setText(_translate("MainWindow", "测试值7"))
        item = self.tableWidgetLeft.verticalHeaderItem(18)
        item.setText(_translate("MainWindow", "误差7"))
        item = self.tableWidgetLeft.verticalHeaderItem(19)
        item.setText(_translate("MainWindow", "测试值8"))
        item = self.tableWidgetLeft.verticalHeaderItem(20)
        item.setText(_translate("MainWindow", "误差8"))
        item = self.tableWidgetLeft.verticalHeaderItem(21)
        item.setText(_translate("MainWindow", "测试值9"))
        item = self.tableWidgetLeft.verticalHeaderItem(22)
        item.setText(_translate("MainWindow", "误差9"))
        item = self.tableWidgetLeft.verticalHeaderItem(23)
        item.setText(_translate("MainWindow", "测试值10"))
        item = self.tableWidgetLeft.verticalHeaderItem(24)
        item.setText(_translate("MainWindow", "误差10"))
        item = self.tableWidgetLeft.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "待测参数"))
        item = self.tableWidgetLeft.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "器件1"))
        item = self.tableWidgetLeft.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "器件2"))
        item = self.tableWidgetLeft.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "器件3"))
        item = self.tableWidgetLeft.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "器件4"))
        item = self.tableWidgetLeft.horizontalHeaderItem(5)
        item.setText(_translate("MainWindow", "器件5"))
        item = self.tableWidgetLeft.horizontalHeaderItem(6)
        item.setText(_translate("MainWindow", "器件6"))
        item = self.tableWidgetLeft.horizontalHeaderItem(7)
        item.setText(_translate("MainWindow", "器件7"))
        item = self.tableWidgetLeft.horizontalHeaderItem(8)
        item.setText(_translate("MainWindow", "器件8"))
        __sortingEnabled = self.tableWidgetLeft.isSortingEnabled()
        self.tableWidgetLeft.setSortingEnabled(False)
        self.tableWidgetLeft.setSortingEnabled(__sortingEnabled)
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tab_4), _translate("MainWindow", "左侧温箱"))
        item = self.tableWidgetRight.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "序列号"))
        item = self.tableWidgetRight.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "扫码等级"))
        item = self.tableWidgetRight.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "是否合格"))
        item = self.tableWidgetRight.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "上钳位"))
        item = self.tableWidgetRight.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "下钳位"))
        item = self.tableWidgetRight.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "测试值1"))
        item = self.tableWidgetRight.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "误差1"))
        item = self.tableWidgetRight.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "测试值2"))
        item = self.tableWidgetRight.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "误差2"))
        item = self.tableWidgetRight.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "测试值3"))
        item = self.tableWidgetRight.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "误差3"))
        item = self.tableWidgetRight.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "测试值4"))
        item = self.tableWidgetRight.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "误差4"))
        item = self.tableWidgetRight.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "测试值5"))
        item = self.tableWidgetRight.verticalHeaderItem(14)
        item.setText(_translate("MainWindow", "误差5"))
        item = self.tableWidgetRight.verticalHeaderItem(15)
        item.setText(_translate("MainWindow", "测试值6"))
        item = self.tableWidgetRight.verticalHeaderItem(16)
        item.setText(_translate("MainWindow", "误差6"))
        item = self.tableWidgetRight.verticalHeaderItem(17)
        item.setText(_translate("MainWindow", "测试值7"))
        item = self.tableWidgetRight.verticalHeaderItem(18)
        item.setText(_translate("MainWindow", "误差7"))
        item = self.tableWidgetRight.verticalHeaderItem(19)
        item.setText(_translate("MainWindow", "测试值8"))
        item = self.tableWidgetRight.verticalHeaderItem(20)
        item.setText(_translate("MainWindow", "误差8"))
        item = self.tableWidgetRight.verticalHeaderItem(21)
        item.setText(_translate("MainWindow", "测试值9"))
        item = self.tableWidgetRight.verticalHeaderItem(22)
        item.setText(_translate("MainWindow", "误差9"))
        item = self.tableWidgetRight.verticalHeaderItem(23)
        item.setText(_translate("MainWindow", "测试值10"))
        item = self.tableWidgetRight.verticalHeaderItem(24)
        item.setText(_translate("MainWindow", "误差10"))
        item = self.tableWidgetRight.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "待测参数"))
        item = self.tableWidgetRight.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "器件1"))
        item = self.tableWidgetRight.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "器件2"))
        item = self.tableWidgetRight.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "器件3"))
        item = self.tableWidgetRight.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "器件4"))
        item = self.tableWidgetRight.horizontalHeaderItem(5)
        item.setText(_translate("MainWindow", "器件5"))
        item = self.tableWidgetRight.horizontalHeaderItem(6)
        item.setText(_translate("MainWindow", "器件6"))
        item = self.tableWidgetRight.horizontalHeaderItem(7)
        item.setText(_translate("MainWindow", "器件7"))
        item = self.tableWidgetRight.horizontalHeaderItem(8)
        item.setText(_translate("MainWindow", "器件8"))
        __sortingEnabled = self.tableWidgetRight.isSortingEnabled()
        self.tableWidgetRight.setSortingEnabled(False)
        self.tableWidgetRight.setSortingEnabled(__sortingEnabled)
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tab_5), _translate("MainWindow", "右侧温箱"))
        self.ledLabel.setText(_translate("MainWindow", "S"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("MainWindow", "测试"))
        self.label_9.setText(_translate("MainWindow", "待查器件序列号"))
        item = self.tableWidget_3.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "测试点1"))
        item = self.tableWidget_3.verticalHeaderItem(1)
        item.setText(_translate("MainWindow", "测试点2"))
        item = self.tableWidget_3.verticalHeaderItem(2)
        item.setText(_translate("MainWindow", "测试点3"))
        item = self.tableWidget_3.verticalHeaderItem(3)
        item.setText(_translate("MainWindow", "测试点4"))
        item = self.tableWidget_3.verticalHeaderItem(4)
        item.setText(_translate("MainWindow", "测试点5"))
        item = self.tableWidget_3.verticalHeaderItem(5)
        item.setText(_translate("MainWindow", "测试点6"))
        item = self.tableWidget_3.verticalHeaderItem(6)
        item.setText(_translate("MainWindow", "测试点7"))
        item = self.tableWidget_3.verticalHeaderItem(7)
        item.setText(_translate("MainWindow", "测试点8"))
        item = self.tableWidget_3.verticalHeaderItem(8)
        item.setText(_translate("MainWindow", "测试点9"))
        item = self.tableWidget_3.verticalHeaderItem(9)
        item.setText(_translate("MainWindow", "测试点10"))
        item = self.tableWidget_3.verticalHeaderItem(10)
        item.setText(_translate("MainWindow", "测试点11"))
        item = self.tableWidget_3.verticalHeaderItem(11)
        item.setText(_translate("MainWindow", "测试点12"))
        item = self.tableWidget_3.verticalHeaderItem(12)
        item.setText(_translate("MainWindow", "测试点13"))
        item = self.tableWidget_3.verticalHeaderItem(13)
        item.setText(_translate("MainWindow", "测试点15"))
        item = self.tableWidget_3.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "温度"))
        item = self.tableWidget_3.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "设定压力"))
        item = self.tableWidget_3.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "测试值"))
        item = self.tableWidget_3.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "转换压力值"))
        item = self.tableWidget_3.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "误差"))
        item = self.tableWidget_5.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "结果"))
        item = self.tableWidget_5.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "测试时间"))
        item = self.tableWidget_5.horizontalHeaderItem(1)
        item.setText(_translate("MainWindow", "扫码等级"))
        item = self.tableWidget_5.horizontalHeaderItem(2)
        item.setText(_translate("MainWindow", "是否合格"))
        item = self.tableWidget_5.horizontalHeaderItem(3)
        item.setText(_translate("MainWindow", "上钳位压力"))
        item = self.tableWidget_5.horizontalHeaderItem(4)
        item.setText(_translate("MainWindow", "上钳位测试值"))
        item = self.tableWidget_5.horizontalHeaderItem(5)
        item.setText(_translate("MainWindow", "下钳位压力"))
        item = self.tableWidget_5.horizontalHeaderItem(6)
        item.setText(_translate("MainWindow", "下钳位测试值"))
        item = self.searchTableWidget.verticalHeaderItem(0)
        item.setText(_translate("MainWindow", "1"))
        item = self.searchTableWidget.horizontalHeaderItem(0)
        item.setText(_translate("MainWindow", "1"))
        self.searchButton.setText(_translate("MainWindow", "查询"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("MainWindow", "查询"))
        self.menu.setTitle(_translate("MainWindow", "文件"))
        self.menu_2.setTitle(_translate("MainWindow", "关于"))
        self.loadConfig.setText(_translate("MainWindow", "导入配置文件"))
        self.saveConfig.setText(_translate("MainWindow", "导出配置文件"))
        self.resPath.setText(_translate("MainWindow", "测试结果保存路径"))
        self.actionbk.setText(_translate("MainWindow", "备份测试数据"))
