# This is a sample Python script.

# Press Shift+F10 to execute it or replace it with your code.
# Press Double Shift to search everywhere for classes, files, tool windows, actions, and settings.

# 导入程序运行必须模块
import sys
# PyQt5中使用的基本控件都在PyQt5.QtWidgets模块中
from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtGui import QIcon
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog, QStyleFactory
import qdarkstyle
from qdarkstyle.light.palette import LightPalette

from mainform import MyMainForm
from serialsetting import ComSerial
import os
import threading
import time
import logging
import json
import logging.config

logging.config.dictConfig(json.load(open('logging_config.json')))  # read logging config from json file

if __name__ == "__main__":
    # 创建日志存储文件夹
    log_folder = 'logs'
    if not os.path.exists(log_folder):
        os.makedirs(log_folder)

    # 固定的，PyQt5程序都需要QApplication对象。sys.argv是命令行参数列表，确保程序可以双击运行
    QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling)
    app = QApplication(sys.argv)
    app.setStyleSheet(qdarkstyle.load_stylesheet(qt_api='pyqt5', palette=LightPalette()))

    # 初始化
    myWin = MyMainForm()
    # 将窗口控件显示在屏幕上
    myWin.setWindowIcon(QIcon("./icon.ico"))
    myWin.show()

    myWin.load_configuration_on_start()
    myWin.scan_serial()
    myWin.updata_serial()

    myWin.resDataPath = myWin.savedPath.text()
    myWin.bckDataPath = myWin.backupPath.text()

    myWin.xlsx_init()

    # myWin.save_test_data(list)
    # s1 = threading.Thread(target=myWin.save_test_data(),args=None)
    # s2 = threading.Thread(target=myWin.save_test_data(), args=None)
    # # s1.start()
    # # s2.start()
    # #time.sleep(2)
    # print(threading.current_thread())
    # print("actie thread", threading.active_count())
    # print(s1.is_alive(), "is alive?")
    # #s1.join()
    # print(threading.current_thread())
    # print("actie thread", threading.active_count())
    # myWin.comboBox_3.clear()
    # myWin.comboBox_3.addItem("Is")
    # 程序运行，sys.exit方法确保程序完整退出。
    sys.exit(app.exec_())

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
