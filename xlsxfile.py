import openpyxl
from openpyxl.styles import Alignment, Border, Side, Color
from openpyxl.styles import PatternFill
# import numpy as np
import os
from datetime import datetime
import math
from ctypes import windll
import win32file
from win32file import *


class Xlsx:
    path = ""
    year = ""
    month = ""

    def __init__(self, path, year, month):
        self.year = year
        self.month = month
        self.path = path + "/NCGK_" + str(year) + "%02d" % month + str("v2.0 ") + ".xlsx"
        print("path is ", self.path)
        # self.path = path
        if (os.path.exists(path) == False):
            os.makedirs(path)
        if os.path.exists(self.path):
            self.wb = openpyxl.load_workbook(self.path)
            self.ws = self.wb.active
            # 检测文件版本
            self.file_version = self.detect_file_version()
            print(f"检测到文件版本: {self.file_version}")
        else:
            self.wb = openpyxl.Workbook()
            self.ws = self.wb.active
            self.file_version = "V2.0"
            self.makeheader()

    def save(self):
        # 保存前检查版本号是否存在
        version_cell = self.ws.cell(1, 1).value
        if not version_cell or "版本" not in str(version_cell):
            print("警告：版本号丢失，重新设置...")
            self.ws.cell(row=1, column=1).value = "版本: V2.0 (含扫码等级)"
            self.ws.cell(row=1, column=1).alignment = Alignment(horizontal='left', vertical='center')

        print(f"保存Excel文件，A1单元格内容: {self.ws.cell(1, 1).value}")
        self.wb.save(self.path)

    def detect_file_version(self):
        """检测Excel文件的版本"""
        try:
            # 检查第一行第一列是否有版本信息
            version_cell = self.ws.cell(1, 1).value
            if version_cell and "版本" in str(version_cell):
                if "V2.0" in str(version_cell):
                    return "V2.0"
                else:
                    return "V2.0"  # 默认为V2.0

            # 检查表头结构来判断版本
            if self.ws.max_row >= 3:
                # 检查第3行（表头行）的列数和内容
                header_row = 3
                headers = []
                for col in range(1, 10):  # 检查前9列
                    cell_value = self.ws.cell(header_row, col).value
                    if cell_value:
                        headers.append(str(cell_value))

                # 如果包含"扫码等级"，则为V2.0版本
                if "扫码等级" in headers:
                    return "V2.0"
                else:
                    return "V1.0"

            return "V1.0"  # 默认为旧版本
        except Exception as e:
            print(f"版本检测失败: {e}")
            return "V1.0"  # 出错时默认为旧版本

    def makeheader(self):
        ws = self.ws

        # 添加版本标识到左上角
        ws.cell(row=1, column=1).value = "版本: V2.0 (含扫码等级)"
        ws.cell(row=1, column=1).alignment = Alignment(horizontal='left', vertical='center')
        print(f"设置版本号到A1单元格: {ws.cell(row=1, column=1).value}")

        start_row = 2  # 从第2行开始放置测试点标题
        width = 5
        for i in range(25):
            ws.merge_cells(start_row=start_row, start_column=i * width + 9, end_row=start_row,
                           end_column=(i + 1) * width - 1 + 9)
            label = "测试点" + str(i + 1)
            ws.cell(row=start_row, column=i * width + 9).value = label
            ws.cell(row=start_row, column=i * width + 9).alignment = Alignment(horizontal='center', vertical='center')
            ws.cell(row=start_row, column=i * width + 9).border = Border(left=Side(style='thick'))
            ws.cell(row=start_row, column=i * width + 13).border = Border(right=Side(style='thick'))

        # 修改表头，增加扫码等级列
        linetwo = ["序列号", "测试时间", "扫码等级", "是否合格", "上钳位压力", "上钳位测试值", "下钳位压力",
                   "下钳位测试值"]
        for i in range(25):
            linetwo.append("温度")
            linetwo.append("设定压力值")
            linetwo.append("测试值")
            linetwo.append("标准值")
            linetwo.append("误差比")
        # print(linetwo)
        ws.append(linetwo)
        for i in range(25):
            self.ws.cell(3, i * 5 + 8 + 1).border = Border(left=Side(style='thick'))
            self.ws.cell(3, i * 5 + 12 + 1).border = Border(right=Side(style='thick'))
        self.wb.save(self.path)

    def searchID(self, productor_id):
        # print(type(id))
        for cell in self.ws['A']:  # 序列号所有在列A
            # print(type(cell.value))
            if cell.value == productor_id:
                return cell.row
        return -1

    def searchID_with_date_check(self, productor_id, test_date):
        """
        根据产品编号和测试日期查找数据行
        如果找到相同编号的数据：
        - 如果是同一天的测试，返回行号（允许覆盖）
        - 如果不是同一天的测试，返回-1（不允许覆盖，需要新增）

        注意：此方法当前未使用，已恢复到简单的同编号覆盖模式
        """
        from datetime import datetime

        # 解析当前测试日期
        try:
            current_test_date = datetime.strptime(test_date, "%Y-%m-%d %H:%M:%S").date()
        except ValueError:
            # 如果日期格式不正确，使用原来的逻辑
            return self.searchID(productor_id)

        # 根据文件版本确定时间列的位置
        time_col = 2  # V1.0和V2.0都是第2列

        for cell in self.ws['A']:  # 序列号都在列A
            if cell.value == productor_id:
                row = cell.row
                # 获取该行的测试时间
                existing_test_time = self.ws.cell(row, time_col).value

                if existing_test_time:
                    try:
                        if isinstance(existing_test_time, str):
                            existing_date = datetime.strptime(existing_test_time, "%Y-%m-%d %H:%M:%S").date()
                        else:
                            # 如果是datetime对象
                            existing_date = existing_test_time.date()

                        # 如果是同一天，允许覆盖
                        if existing_date == current_test_date:
                            print(f"找到同一天的测试数据，行号: {row}, 日期: {existing_date}")
                            return row
                        else:
                            print(
                                f"找到不同天的测试数据，行号: {row}, 已有日期: {existing_date}, 当前日期: {current_test_date}")
                            # 继续查找，可能有同一天的其他记录
                            continue
                    except (ValueError, AttributeError):
                        # 如果日期解析失败，使用原来的逻辑
                        return row
                else:
                    # 如果没有测试时间，使用原来的逻辑
                    return row

        return -1

    def append_data(self, datalist):
        self.ws._current_row = self.ws.max_row
        # 处理数据类型，确保数值数据以正确的类型存储
        processed_datalist = []
        for i, value in enumerate(datalist):
            if i >= 4 and value != '' and isinstance(value, str):  # 调整索引，因为增加了扫码等级列
                # 对于索引4及以后的数值字段，尝试转换为数值类型
                if value.replace('.', '').replace('-', '').replace('%', '').isdigit():
                    try:
                        if '%' in value:
                            # 百分比数据，去掉%号并转换为小数，保持原始精度
                            processed_datalist.append(float(value.replace('%', '')) / 100)
                        else:
                            # 判断是否为误差值（每5列的第5列，即误差比列）
                            if i >= 8 and (i - 8) % 5 == 4:  # 误差比列，保持原始精度
                                processed_datalist.append(float(value))
                            else:
                                # 其他数值，保持四位小数精度
                                processed_datalist.append(round(float(value), 4))
                    except ValueError:
                        processed_datalist.append(value)
                else:
                    processed_datalist.append(value)
            elif i >= 4 and value != '' and isinstance(value, (int, float)):
                # 如果已经是数值类型
                if i >= 8 and (i - 8) % 5 == 4:  # 误差比列，保持原始精度
                    processed_datalist.append(float(value))
                else:
                    # 其他数值，格式化为四位小数
                    processed_datalist.append(round(float(value), 4))
            else:
                processed_datalist.append(value)

        # 调试：打印要写入Excel的数据
        if len(processed_datalist) >= 4:
            print(
                f"写入Excel数据: 序列号='{processed_datalist[0]}', 测试时间='{processed_datalist[1]}', 扫码等级='{processed_datalist[2]}', 是否合格='{processed_datalist[3]}'")

        self.ws.append(processed_datalist)

        # 设置数值列的格式
        current_row = self.ws.max_row

        # 设置上钳位测试值和下钳位测试值为四位小数格式（第6列和第8列）
        for col in [6, 8]:
            cell = self.ws.cell(current_row, col)
            if isinstance(cell.value, (int, float)):
                cell.number_format = '0.0000'

        # 设置测试点数据的格式
        for i in range(25):  # 最多25个测试点
            base_col = 9 + i * 5  # 每个测试点的起始列
            # 测试值（第3列）和标准值（第4列）设置为四位小数
            for offset in [2, 3]:  # 测试值和标准值
                col = base_col + offset
                if col <= len(processed_datalist) + 8:
                    cell = self.ws.cell(current_row, col)
                    if isinstance(cell.value, (int, float)):
                        cell.number_format = '0.0000'

            # 误差比（第5列）设置为四位小数的百分比
            percent_col = base_col + 4
            if percent_col <= len(processed_datalist) + 8:
                cell = self.ws.cell(current_row, percent_col)
                if isinstance(cell.value, (int, float)):
                    cell.number_format = '0.0000%'

        return current_row
        # time_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # print(time_now)

    def merge_data(self, row, datalist):
        # column_num = len([i.value for i in self.ws[row] if i.value])
        i = 0
        column_num = 0
        for cell in self.ws[row]:
            i = i + 1
            if i < 9:  # 调整为9，因为增加了扫码等级列
                column_num = column_num + 1
                continue
            if cell.value:
                column_num = column_num + 1

        test_points_xls = math.floor((column_num - 8) / 5)  # 调整为8

        # 更新测试时间
        if len(datalist) > 1 and datalist[1] != '':
            self.ws.cell(row, 2).value = datalist[1]

        # 更新扫码等级（如果有的话）
        if len(datalist) > 2 and datalist[2] != '':
            self.ws.cell(row, 3).value = datalist[2]
            print(f"更新扫码等级到第3列: '{datalist[2]}'")

        # 更新合格状态（无论是合格还是不合格都要更新）
        if len(datalist) > 3 and datalist[3] != '':
            self.ws.cell(row, 4).value = datalist[3]
            print(f"更新合格状态到第4列: '{datalist[3]}'")
        else:
            print(f"警告：合格状态为空或无效: '{datalist[3] if len(datalist) > 3 else 'N/A'}'")

        # 更新钳位数据，保持四位小数精度
        for i in range(4):
            if len(datalist) > 4 + i and datalist[4 + i] != '':
                # 确保数值数据以正确的类型存储并格式化为四位小数
                value = datalist[4 + i]
                if isinstance(value, str) and value.replace('.', '').replace('-', '').isdigit():
                    try:
                        value = round(float(value), 4)
                    except ValueError:
                        pass
                elif isinstance(value, (int, float)):
                    value = round(float(value), 4)

                cell = self.ws.cell(row, 5 + i)
                cell.value = value

                # 为上钳位测试值和下钳位测试值设置四位小数格式（第6列和第8列）
                if 5 + i in [6, 8] and isinstance(value, (int, float)):
                    cell.number_format = '0.0000'

        # 如果Excel中没有测试点数据，但仍需要更新基本信息
        if test_points_xls <= 0:
            # 仍然需要处理新的测试点数据（如果有的话）
            test_points_data = math.floor((len(datalist) - 8) / 5)  # 调整为8
            if test_points_data > 0:
                # 直接添加新的测试点数据到行末，保持四位小数精度
                for i in range(test_points_data * 5):
                    value = datalist[i + 8]  # 调整为8，因为增加了扫码等级列
                    # 处理数值类型
                    if isinstance(value, str) and value != '':
                        if value.replace('.', '').replace('-', '').replace('%', '').isdigit():
                            try:
                                if '%' in value:
                                    value = float(value.replace('%', '')) / 100
                                else:
                                    # 判断是否为误差值（每5列的第5列）
                                    if (i + 1) % 5 == 0:  # 误差比列，保持原始精度
                                        value = float(value)
                                    else:
                                        value = round(float(value), 4)
                            except ValueError:
                                pass
                    elif isinstance(value, (int, float)):
                        # 判断是否为误差值（每5列的第5列）
                        if (i + 1) % 5 == 0:  # 误差比列，保持原始精度
                            value = float(value)
                        else:
                            value = round(float(value), 4)

                    cell = self.ws.cell(row, column_num + i + 1)
                    cell.value = value

                    # 设置数值格式
                    if isinstance(value, (int, float)):
                        if (i + 1) % 5 == 0:  # 误差比列
                            cell.number_format = '0.0000%'
                        elif (i + 1) % 5 in [3, 4]:  # 测试值和标准值列
                            cell.number_format = '0.0000'
            return True
        test_points_data = math.floor((len(datalist) - 8) / 5)  # 调整为8
        if test_points_data <= 0:
            # 即使没有测试点数据，也要返回True表示基本信息已更新
            return True
        print("before merge datalist is ", datalist)
        # print("test_point ", test_points_data)
        print("test_points_xls ", test_points_xls)
        print("column_num", column_num)
        # print("len(datalist)", len(datalist))

        # 更新测试时间到最新
        self.ws.cell(row, 2).value = datalist[1]

        del_flags = []
        for j in range(test_points_data):
            del_flags.append(False)
        for i in range(test_points_xls):
            print("i is ", i)
            print("i is ", list(range(test_points_xls)))
            for j in range(test_points_data):
                # 获取Excel中的温度和压力值（调整列索引）
                excel_temp = self.ws.cell(row, i * 5 + 8 + 1).value  # 调整+1
                excel_pressure = self.ws.cell(row, i * 5 + 9 + 1).value  # 调整+1
                data_temp = datalist[j * 5 + 8]  # 调整+1
                data_pressure = datalist[j * 5 + 9]  # 调整+1

                print("to judge is ", excel_temp, data_temp, excel_pressure, data_pressure)

                # 数值比较，考虑类型转换
                temp_match = False
                pressure_match = False

                try:
                    if isinstance(excel_temp, (int, float)) and isinstance(data_temp, (int, float)):
                        temp_match = abs(excel_temp - data_temp) < 1e-6
                    else:
                        temp_match = str(excel_temp) == str(data_temp)

                    if isinstance(excel_pressure, (int, float)) and isinstance(data_pressure, (int, float)):
                        pressure_match = abs(excel_pressure - data_pressure) < 1e-6
                    else:
                        pressure_match = str(excel_pressure) == str(data_pressure)
                except (TypeError, ValueError):
                    temp_match = str(excel_temp) == str(data_temp)
                    pressure_match = str(excel_pressure) == str(data_pressure)

                if temp_match and pressure_match:
                    # 更新测试值、标准值和误差比（调整索引），保持四位小数精度
                    test_value = datalist[j * 5 + 10]  # 调整+1
                    ref_value = datalist[j * 5 + 11]  # 调整+1
                    error_value = datalist[j * 5 + 12]  # 调整+1

                    # 处理数值类型并格式化为四位小数
                    if isinstance(test_value, str) and test_value.replace('.', '').replace('-', '').isdigit():
                        try:
                            test_value = round(float(test_value), 4)
                        except ValueError:
                            pass
                    elif isinstance(test_value, (int, float)):
                        test_value = round(float(test_value), 4)

                    if isinstance(ref_value, str) and ref_value.replace('.', '').replace('-', '').isdigit():
                        try:
                            ref_value = round(float(ref_value), 4)
                        except ValueError:
                            pass
                    elif isinstance(ref_value, (int, float)):
                        ref_value = round(float(ref_value), 4)

                    if isinstance(error_value, str) and error_value != '':
                        if error_value.replace('.', '').replace('-', '').replace('%', '').isdigit():
                            try:
                                if '%' in error_value:
                                    error_value = float(error_value.replace('%', '')) / 100
                                else:
                                    error_value = float(error_value)  # 保持原始精度
                            except ValueError:
                                pass
                    elif isinstance(error_value, (int, float)):
                        error_value = float(error_value)  # 保持原始精度，不进行四舍五入

                    # 设置单元格值和格式
                    test_cell = self.ws.cell(row, i * 5 + 10 + 1)  # 调整+1
                    test_cell.value = test_value
                    if isinstance(test_value, (int, float)):
                        test_cell.number_format = '0.0000'

                    ref_cell = self.ws.cell(row, i * 5 + 11 + 1)  # 调整+1
                    ref_cell.value = ref_value
                    if isinstance(ref_value, (int, float)):
                        ref_cell.number_format = '0.0000'

                    error_cell = self.ws.cell(row, i * 5 + 12 + 1)  # 调整+1
                    error_cell.value = error_value
                    # 为误差比列设置四位小数百分比格式
                    if isinstance(error_value, (int, float)):
                        error_cell.number_format = '0.0000%'

                    del_flags[j] = True
                    print("the del fags is ", j, del_flags[j])
                    # del datalist[(j * 5 + 7):(j * 5 + 12)]
                    break
        for j in range(test_points_data - 1, -1, -1):
            print("j is ", j, "flag is ", del_flags[j])
            if del_flags[j]:
                del datalist[(j * 5 + 8):(j * 5 + 13)]  # 调整索引
        print("datalist is ", datalist)
        test_points_data = math.floor((len(datalist) - 8) / 5)  # 调整为8
        if test_points_data <= 0:
            return True
        for i in range(test_points_data * 5):
            value = datalist[i + 8]  # 调整为8
            # 处理数值类型
            if isinstance(value, str) and value != '':
                if value.replace('.', '').replace('-', '').replace('%', '').isdigit():
                    try:
                        if '%' in value:
                            # 百分比数据，去掉%号并转换为小数，保持原始精度
                            value = float(value.replace('%', '')) / 100
                        else:
                            # 判断是否为误差值（每5列的第5列）
                            if (i + 1) % 5 == 0:  # 误差比列，保持原始精度
                                value = float(value)
                            else:
                                value = round(float(value), 4)
                    except ValueError:
                        pass
            elif isinstance(value, (int, float)):
                # 判断是否为误差值（每5列的第5列）
                if (i + 1) % 5 == 0:  # 误差比列，保持原始精度
                    value = float(value)
                else:
                    value = round(float(value), 4)

            cell = self.ws.cell(row, column_num + i + 1)
            cell.value = value

            # 设置数值格式
            if isinstance(value, (int, float)):
                if (i + 1) % 5 == 0:  # 误差比列（每5列的第5列）
                    cell.number_format = '0.0000%'
                elif (i + 1) % 5 in [3, 4]:  # 测试值和标准值列（每5列的第3、4列）
                    cell.number_format = '0.0000'

            # print("to merge data is ", cell.value)
        return True

    def fill_color(self, row, ui):
        i = 0
        column_num = 0
        for cell in self.ws[row]:
            i = i + 1
            if i < 8:
                column_num = column_num + 1
                continue
            if cell.value:
                column_num = column_num + 1
        pressure_down_vols = [ui.doubleSpinBox_12.value(), ui.doubleSpinBox_15.value()]
        pressure_up_vols = [ui.doubleSpinBox_13.value(), ui.doubleSpinBox_14.value()]

        low_temp_error_range_down = ui.doubleSpinBox_5.value() / 100
        mid_temp_error_range_down = ui.doubleSpinBox_6.value() / 100
        high_temp_error_range_down = ui.doubleSpinBox_7.value() / 100
        low_temp_error_range_up = ui.doubleSpinBox_31.value() / 100
        mid_temp_error_range_up = ui.doubleSpinBox_32.value() / 100
        high_temp_error_range_up = ui.doubleSpinBox_33.value() / 100
        low_mid_temp = ui.doubleSpinBox_8.value()
        mid_high_temp = ui.doubleSpinBox_9.value()
        fillred = PatternFill('solid', fgColor="E20A0A")
        NGfalg = False
        if column_num >= 4:
            if self.ws.cell(row, 4).value == '不合格':
                self.ws.cell(row, 4).fill = fillred
        if column_num >= 8:
            for i, down_vol, up_vol in zip(range(2), pressure_down_vols, pressure_up_vols):
                # print("cell row", i*2+5, self.ws.cell(row, i * 2 + 5).value)
                if self.ws.cell(row, i * 2 + 6).value is not None and self.ws.cell(row, i * 2 + 6).value != '':
                    # print(self.ws.cell(row, i * 2 + 5).value)
                    vol = float(self.ws.cell(row, i * 2 + 6).value)
                    if vol > up_vol or vol < down_vol:
                        self.ws.cell(row, i * 2 + 6).fill = fillred
                        NGfalg = True
        else:
            return False
        test_points_xls = math.floor((column_num - 7) / 5)
        if test_points_xls <= 0:
            return False
        for i in range(test_points_xls):
            # 补充边界坚线
            self.ws.cell(row, i * 5 + 8 + 1).border = Border(left=Side(style='thick'))
            self.ws.cell(row, i * 5 + 12 + 1).border = Border(right=Side(style='thick'))
            percent_str = self.ws.cell(row, i * 5 + 11 + 2).value
            # error = float(self.ws.cell(row, i * 5 + 11 + 1).value)

            # 处理不同的数据类型
            if isinstance(percent_str, str):
                # 如果是字符串，可能包含%号，需要去除
                error = float(percent_str.rstrip("%")) / 100
            elif isinstance(percent_str, (int, float)):
                # 如果已经是数值类型，直接使用
                error = float(percent_str)
            else:
                # 其他情况，尝试转换为字符串再处理
                error = float(str(percent_str).rstrip("%")) / 100
            # print(self.ws.cell(row, i * 5 + 7 + 1).value)
            temperature = float(self.ws.cell(row, i * 5 + 9).value)
            if temperature > mid_high_temp:  # 高温
                if error > high_temp_error_range_up or error < high_temp_error_range_down:
                    self.ws.cell(row, i * 5 + 11 + 2).fill = fillred
                    NGfalg = True
            elif temperature > low_mid_temp:  # 常温
                if error > mid_temp_error_range_up or error < mid_temp_error_range_down:
                    self.ws.cell(row, i * 5 + 11 + 2).fill = fillred
                    NGfalg = True
            else:
                if error > low_temp_error_range_up or error < low_temp_error_range_down:
                    self.ws.cell(row, i * 5 + 11 + 2).fill = fillred
                    NGfalg = True
        if NGfalg is False and self.ws.cell(row, 3).value == '不合格':
            self.ws.cell(row, 3).value = '合格'
            self.ws.cell(row, 3).fill = PatternFill(fgColor="00FFFFFF")

    def is_open(self):

        try:
            # 首先获得句柄
            vHandle = win32file.CreateFile(self.path, GENERIC_READ, 0, None, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, None)
            # 判断句柄是否等于INVALID_HANDLE_VALUE
            if int(vHandle) == INVALID_HANDLE_VALUE:
                print("# file is already open")
                return True  # file is already open
            win32file.CloseHandle(vHandle)

        except Exception as e:
            print(e)
            return True

        return False

    def _float_conversion(self, number, type):
        if type == 'normal':
            return f"{number:.4f}"
        else:
            return f"{number * 100:.4f}"


import filecmp
import shutil
import os


class Copytobackup:

    def __init__(self, originpath, destpath):
        self.originpath = originpath
        self.destpath = destpath

    def backupfile(self):
        files_tatus = filecmp.dircmp(self.originpath, self.destpath)
        for file in files_tatus.left_only:
            originfilepath = os.path.join(self.originpath, file)
            destfilepath = os.path.join(self.destpath, file)
            shutil.copy(originfilepath, destfilepath)
        for file in files_tatus.diff_files:
            originfilepath = os.path.join(self.originpath, file)
            destfilepath = os.path.join(self.destpath, file)
            shutil.copy2(originfilepath, destfilepath)
        del files_tatus


if __name__ == '__main__':
    a1 = Xlsx("test.xlsx", 2024, 8)
    # print(a1.searchID('A126'))
    # a1.ws.cell(3, 6).border = Border(left=Side(style='thick'))
    # a1.ws.cell(3, 7).border = Border(right=Side(style='thick'))
    # a1.ws.cell(3, 6).fill = PatternFill('solid', fgColor="E20A0A")
    # a1.ws.cell(3, 6).fill = PatternFill( fgColor="00FFFFFF")
    t1 = 0.02124534
    s1 = a1._float_conversion(t1, "normal")
    print(type(s1))
    datalist = ['0023233', '', '', '4.2232', '8.5665', '20.110', '30.5']
    datalist.append(float('%.6f' % (float(t1))))
    # datalist.append(float(t1))
    a1.ws.append(datalist)
    a1.ws.cell(12, 8).number_format = '0.0000%'
    a1.save()
    # datalist = ['2','6']
    # a1.wb.save("test.xlsx")
    # a1.merge_data(6, datalist)
    # te = Copytobackup('C:\\Users\\<USER>\\Downloads\\testdata','C:\\NCGK')
    # te.backupfile()
    time_now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(time_now)
