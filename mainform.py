import plc
import serialsetting
import voltage
from uitest import Ui_MainWindow
from PyQt5.QtWidgets import QApplication, QMainWindow, QFileDialog, QTabWidget, QTableWidgetItem, QHeaderView, \
    QScrollArea, QPushButton, QDialog, QVBoxLayout, QMessageBox, QLabel
from PyQt5 import QtCore, QtGui, QtWidgets
from PyQt5.QtCore import pyqtSignal, QObject, QSemaphore, QTimer
from PyQt5.QtWidgets import QTextBrowser, QMessageBox
import serial
import serial.tools.list_ports
from pressure import PressureControl
import relay
from temperature import TempModbus
from SENT import SENT
import datetime
import xlsxfile
import openpyxl
from openpyxl.styles import PatternFill
import time
import scan
import threading
import logging
import logging.config
from log_exception import TemperatureError, PlcError, PressureSourceError, VoltageError, RelayError, SENTError
from logger import QTextBrowserHandler, QMessageBoxHandler, log_instance
import sys, os
import json
import math
import numpy as np

# 日志原则：只有这里的logger才会输出到图形界面的日志窗口和弹窗
# 如果是自定义的exception，考虑是否中断程序，如果中断，直接抛出异常，否则只记录日志
mylogger = logging.getLogger('mainform')


class MyMainForm(QMainWindow, Ui_MainWindow):
    testDialogSignal = pyqtSignal(str, str)

    @log_instance('初始化图形界面', mylogger)
    def __init__(self, parent=None):
        super(MyMainForm, self).__init__(parent)
        self.setupUi(self)

        self.tabWidget.currentChanged[int].connect(
            lambda currentChanged: self.tab_change(currentChanged))  # type: ignore

        self.pushButton_2.clicked.connect(lambda: self.start_test_thread())
        self.pushButton_5.clicked.connect(lambda: self.clean_test_data())
        self.terminateButton.clicked.connect(lambda: self.closeTestThread())

        # 初始化测试状态和计时器
        self.init_test_status_display()
        self.init_test_timer()
        # self.tableWidgetLeft.cellDoubleClicked['int', 'int'].connect(lambda: self.scan_place_update())
        self.loadConfig.triggered.connect(lambda: self.load_configuration())
        self.saveConfig.triggered.connect(lambda: self.save_configuration())

        # 主窗口设置
        self.setWindowTitle('NCGK测试系统' + ' ' + '未初始化配置文件...')
        self.setCentralWidget(self.scrollArea)

        # 单击触发
        self.tableWidgetLeft.cellClicked.connect(
            lambda row, column: self.scan_place_update(row, column, 'Left'))
        ##左侧温箱表格
        self.tableWidgetRight.cellClicked.connect(
            lambda row, column: self.scan_place_update(row, column, 'Right'))
        ##右侧温箱表格
        # 扫码进入查询框
        self.searchTableWidget.cellClicked.connect(
            lambda row, column: self.scan_place_update(0, 0, 'Search'))

        self.scan_place = 'Left'
        self.scan_column = 1
        self.scan_thread_flag = False
        self.scan_flag = False  # 禁止扫码
        self.scanthread = threading.Thread(target=self.scan_thread)
        self.test_thread_flag = False  ##测试线程运行标志

        # 扫码等级点击扫码功能
        self.scan_grade_mode = False  # 是否处于扫码等级模式
        self.scan_grade_target = None  # 目标单元格 (table, row, col)

        # 为表格添加点击事件监听
        self.setup_table_click_events()
        # 标志位，表示是否在不合格品处理中
        self.unqualified_process_flag = False

        # testthread = threading.Thread(target=self.start_test)
        self.textBrowser.verticalScrollBar().setValue(50)
        ##设置垂直滚动条
        # 临时先存储的port_list 右烘箱和扫码目前不确定，随便写的，到时候更新
        self.serial_port_choice_idx = {'压力源串口号': 0, 'SENT串口号': 1, '电压表串口号': 2, '左继电器串口号': 3,
                                       '右继电器串口号': 4, '测量模式串口号': 5, '左温箱串口号': 6, '右温箱串口号': 7,
                                       '扫码枪串口号': 8, 'PLC串口号': 9}
        self.serial_port_choice = ['COM11', 'COM3', 'COM8', 'COM9', 'COM7', 'COM5', 'COM14', 'COM13', 'COM10', 'COM12']

        # 用于存储配置文件中的数据, 每次如果有新的内容需要存储, 加上一项即可
        ##读取配置文件中的数据存为字典
        self.config_dict = {
            '上钳位压力': self.doubleSpinBox_10,
            '下钳位压力': self.doubleSpinBox_11,
            '上钳位输出下限': self.doubleSpinBox_12,
            '下钳位输出下限': self.doubleSpinBox_15,
            '上钳位输出上限': self.doubleSpinBox_13,
            '下钳位输出上限': self.doubleSpinBox_14,
            '温度允许波动范围': self.doubleSpinBox_2,
            '压力转换公式截距': self.doubleSpinBox_3,
            '压力转换公式斜率': self.doubleSpinBox_4,
            '低温正误差要求': self.doubleSpinBox_31,
            '中温正误差要求': self.doubleSpinBox_32,
            '高温正误差要求': self.doubleSpinBox_33,
            '低温负误差要求': self.doubleSpinBox_5,
            '中温负误差要求': self.doubleSpinBox_6,
            '高温负误差要求': self.doubleSpinBox_7,
            '低温中温界点': self.doubleSpinBox_8,
            '中温高温界点': self.doubleSpinBox_9,
            '误差对比值': self.doubleSpinBox_16,
            '左烘箱温度': self.TempLeft,
            '右烘箱温度': self.TempRight,
            '测试点数': self.TestPoint,
            '是否钳位测试': self.Model_2,
            '测试采集模式': self.Model,
            '测试点': self.tableWidget_2,
            '压力源串口号': self.SerialPressure,
            'SENT串口号': self.SerialSENT,
            '电压表串口号': self.SerialVol,
            '左继电器串口号': self.Serial8left,
            '右继电器串口号': self.Serial8right,
            '测量模式串口号': self.Serial2,
            '左温箱串口号': self.SerialTempLeft,
            '右温箱串口号': self.SerialTempRight,
            '扫码枪串口号': self.SerialScan,
            'PLC串口号': self.SerialPLC,
            '压力源波特率': self.spinBox_3,
            'SENT波特率': self.spinBox_4,
            '电压表波特率': self.spinBox_5,
            '左继电器波特率': self.spinBox_6,
            '右继电器波特率': self.spinBox_7,
            '测量模式波特率': self.spinBox_8,
            '左温箱波特率': self.spinBox_9,
            '右温箱波特率': self.spinBox_10,
            '扫码枪波特率': self.spinBox_11,
            'PLC波特率': self.spinBox_12,
            '测试结果保存路径': self.savedPath,
            '备份数据保存路径': self.backupPath
            # '配置文件路径': '未初始化配置文件...' # 唯一一个不是qt对象的配置项，而且immutable, 需要特殊处理
        }

        # 初始化允许测试的等级列表，默认为A、B、C、D
        self.allowed_test_grades = ["A", "B", "C", "D"]
        ##串口设备集合
        self.serialBoxSet = {self.SerialPressure, self.SerialSENT, self.SerialVol, self.Serial8left, self.Serial8right,
                             self.Serial2, self.SerialTempLeft, self.SerialTempRight, self.SerialScan, self.SerialPLC}
        self.configNum = len(self.config_dict)
        ##保存路径按钮点击事件
        self.savedPathButton.clicked.connect(lambda: self.savedPathButton_clicked())
        ##备份路径按钮点击事件
        self.backupPathButton.clicked.connect(lambda: self.backupPathButton_clicked())

        # 配置日志输出窗口
        self.add_textbrowser_handler_to_logger()

        # 查询表格
        # self.searchTableWidget.cellChanged.connect(lambda row, col: self.search_table_update(row, col))
        self.searchButton.clicked.connect(lambda: self.search_test_result(self.searchTableWidget.item(0, 0).text()))
        self.workSheetHeader = {'序列号': 0, '测试时间': 1, '扫码等级': 2, '是否合格': 3, '上钳位压力': 4,
                                '上钳位测试值': 5, '下钳位压力': 6, '下钳位测试值': 7}

        # led灯
        self.setLed(self.ledLabel, 'gray', 64)

        # 修改单元格背景色，测试功能
        # self.tableWidget_3.setItem(0, 0, QTableWidgetItem('1'))
        # self.tableWidget_3.item(0, 0).setBackground(QtGui.QColor(255, 0, 0))
        # self.tableWidget_3.item(0, 0).setForeground(QtGui.QColor(0, 255, 0))

        # 为spinbox, combobox过滤掉wheel事件
        self.filterBoxWheelEvent()

        # 添加压力间隔模式参数变化监听
        self.setup_pressure_mode_listeners()

        # 信号量, 让子线程等待主线程
        self.testSem = QSemaphore(0)
        self.testDialogSignal.connect(self._show_dialog)
        self.testDialogResult = None

    # 过滤掉wheel事件的filter
    def eventFilter(self, obj, event):
        if isinstance(obj, QtWidgets.QSpinBox) or isinstance(obj, QtWidgets.QDoubleSpinBox) or isinstance(obj,
                                                                                                          QtWidgets.QComboBox):
            if event.type() == QtCore.QEvent.Wheel:
                return True
        return super().eventFilter(obj, event)

    # 过滤所有spinbox, combobox的wheel事件
    def filterBoxWheelEvent(self):
        # spinbox
        self.TempLeft.installEventFilter(self)
        self.TempRight.installEventFilter(self)
        self.TestPoint.installEventFilter(self)
        self.spinBox_3.installEventFilter(self)
        self.spinBox_4.installEventFilter(self)
        self.spinBox_5.installEventFilter(self)
        self.spinBox_6.installEventFilter(self)
        self.spinBox_7.installEventFilter(self)
        self.spinBox_8.installEventFilter(self)
        self.spinBox_9.installEventFilter(self)
        self.spinBox_10.installEventFilter(self)
        self.spinBox_11.installEventFilter(self)
        self.spinBox_12.installEventFilter(self)
        self.doubleSpinBox_3.installEventFilter(self)
        self.doubleSpinBox_4.installEventFilter(self)
        self.doubleSpinBox_5.installEventFilter(self)
        self.doubleSpinBox_10.installEventFilter(self)
        self.doubleSpinBox_11.installEventFilter(self)
        self.doubleSpinBox_12.installEventFilter(self)
        self.doubleSpinBox_13.installEventFilter(self)
        self.doubleSpinBox_14.installEventFilter(self)
        self.doubleSpinBox_15.installEventFilter(self)
        self.doubleSpinBox_16.installEventFilter(self)
        self.doubleSpinBox_31.installEventFilter(self)
        self.doubleSpinBox_32.installEventFilter(self)
        self.doubleSpinBox_33.installEventFilter(self)
        self.doubleSpinBox_6.installEventFilter(self)
        self.doubleSpinBox_7.installEventFilter(self)
        self.doubleSpinBox_8.installEventFilter(self)
        self.doubleSpinBox_9.installEventFilter(self)
        self.doubleSpinBox_2.installEventFilter(self)
        # combobox
        self.SerialPressure.installEventFilter(self)
        self.SerialSENT.installEventFilter(self)
        self.SerialVol.installEventFilter(self)
        self.Serial8left.installEventFilter(self)
        self.Serial8right.installEventFilter(self)
        self.Serial2.installEventFilter(self)
        self.SerialTempLeft.installEventFilter(self)
        self.SerialTempRight.installEventFilter(self)
        self.SerialScan.installEventFilter(self)
        self.SerialPLC.installEventFilter(self)
        self.Model.installEventFilter(self)
        self.Model_2.installEventFilter(self)

    def reset_test_config(self):
        mylogger.info("复位中...")
        # 测试flag
        self.test_thread_flag = False
        # 重启扫码线程
        self.scan_place = 'Left'
        self.scan_column = 1
        self.scan_flag = True
        # 更新LED灯
        self.setLed(self.ledLabel, 'gray', 64)
        # 退出不合格品处理
        self.unqualified_process_flag = False
        # 恢复按钮状态
        self.set_button_in_test(True)
        # 卸载压力
        try:
            self.presscontrol.vent()
            self.plccontrol.plc_assert_action('left', 0)
            self.plccontrol.plc_assert_action('right', 0)
        except PressureSourceError as e:
            mylogger.error(f'{e}, 复位过程中压力卸载失败')
            self.setLed(self.ledLabel, 'yellow', 64)

        # 释放串口资源，防止下次测试时出现"Port is already open"错误
        ##try:
        ##    mylogger.info("释放串口资源...")
        ##    self.release_serial_resources()
        ##except Exception as e:
        ##    mylogger.warning(f"释放串口资源时出现异常: {e}")

    def clear_test_parameter_column(self):
        """清空第一列（第0列）的待测参数"""
        try:
            # 获取表格的行数
            left_row_count = self.tableWidgetLeft.rowCount()
            right_row_count = self.tableWidgetRight.rowCount()
            left_column_count = self.tableWidgetLeft.columnCount()
            right_column_count = self.tableWidgetRight.columnCount()

            # 清空左侧表格第0列的所有内容
            for row in range(left_row_count):
                if self.tableWidgetLeft.item(row, 0) is not None:
                    self.tableWidgetLeft.setItem(row, 0, QTableWidgetItem(""))

            # 清空右侧表格第0列的所有内容
            for row in range(right_row_count):
                if self.tableWidgetRight.item(row, 0) is not None:
                    self.tableWidgetRight.setItem(row, 0, QTableWidgetItem(""))
            ##清空第一行的所有内容
            for column in range(left_column_count):
                if self.tableWidgetLeft.item(0, column) is not None:
                    self.tableWidgetLeft.setItem(0, column, QTableWidgetItem(""))
            for column in range(right_column_count):
                if self.tableWidgetRight.item(0, column) is not None:
                    self.tableWidgetRight.setItem(0, column, QTableWidgetItem(""))

            # 更新表格显示
            self.tableWidgetLeft.viewport().update()
            self.tableWidgetRight.viewport().update()

            mylogger.debug("第一列待测参数已清空")

        except Exception as e:
            mylogger.warning(f"清空第一列待测参数失败: {e}")

    def reset_test_interface_state(self):
        """重置测试界面状态，确保LED和按钮颜色正确显示"""
        try:
            # 重置LED灯为灰色（待机状态）
            self.setLed(self.ledLabel, 'gray', 64)

            # 确保按钮状态正确（启用状态）
            self.set_button_in_test(True)

            # 重置测试状态显示
            self.update_test_status("待机中", "#f0f0f0")

            # 重置计时器显示
            if hasattr(self, 'test_timer'):
                self.reset_test_timer()

            # 重置扫码相关状态
            self.scan_place = 'Left'
            self.scan_column = 1
            self.scan_flag = True

            # 重置测试线程标志
            self.test_thread_flag = False

            mylogger.debug("测试界面状态已重置")

        except Exception as e:
            mylogger.warning(f"重置测试界面状态失败: {e}")

    def release_serial_resources(self):
        """释放所有串口资源，防止端口占用"""
        mylogger.debug("开始释放串口资源...")

        # 释放各个硬件设备的串口连接
        devices_to_release = [
            ('presscontrol', '压力控制器'),
            ('sent', 'SENT通信'),
            ('vol', '电压表'),
            ('relay8left', '左继电器'),
            ('relay8right', '右继电器'),
            ('relay2', '2路继电器'),
            ('templeftread', '左温度控制器'),
            ('temprightread', '右温度控制器'),
            ('scanid', '扫码枪'),
            ('plccontrol', 'PLC控制器')
        ]

        for device_attr, device_name in devices_to_release:
            try:
                if hasattr(self, device_attr):
                    device = getattr(self, device_attr)
                    if device:
                        # 处理不同类型的串口连接
                        if hasattr(device, 'serial'):
                            # 对于使用ComSerial包装的设备（如压力控制器、继电器等）
                            if hasattr(device.serial, 'ser') and hasattr(device.serial.ser, 'close'):
                                if device.serial.ser.is_open:
                                    device.serial.ser.close()
                                    mylogger.debug(f"{device_name} 串口已关闭")
                            # 对于直接使用serial.Serial的设备（如SENT）
                            elif hasattr(device.serial, 'close') and hasattr(device.serial, 'is_open'):
                                if device.serial.is_open:
                                    device.serial.close()
                                    mylogger.debug(f"{device_name} 串口已关闭")
                        elif hasattr(device, 'conn'):
                            # 对于PLC等使用conn属性的设备
                            if hasattr(device.conn, 'close') and device.conn.is_open:
                                device.conn.close()
                                mylogger.debug(f"{device_name} 连接已关闭")
            except Exception as e:
                mylogger.debug(f"释放 {device_name} 串口资源时出现异常: {e}")

        mylogger.debug("串口资源释放完成")

    def closeTestThread(self):
        # 关闭测试线程
        if hasattr(self, 'testthread') and self.testthread.is_alive():
            self.test_thread_flag = False
            mylogger.error("正在尝试终止, 请等待当前操作执行完毕...")
            mylogger.info("测试线程已经关闭!")
            # 停止计时器但保留时间显示
            ### self.stop_test_timer_keep_display()
            self.update_test_status("已终止", "#f8d7da")
            self.reset_test_config()
        else:
            mylogger.info("不在测试中!")
            # 停止计时器但保留时间显示
            ###   self.stop_test_timer_keep_display()
            self.update_test_status("待机中", "#f0f0f0")

    def closeEvent(self, event):
        mylogger.debug("Closing the application")
        # 询问是否退出
        reply = QtWidgets.QMessageBox.question(self, '退出', '是否要退出程序？',
                                               QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
                                               QtWidgets.QMessageBox.No)
        if reply == QtWidgets.QMessageBox.Yes:
            # 保存配置文件
            self.save_configuration_on_exit()
            mylogger.debug("Exiting the application")
            # 关闭所有线程
            mylogger.debug("Closing all threads...")
            self.scan_thread_flag = False
            if self.scanthread.is_alive():
                self.scanthread.join()
            if hasattr(self, 'testthread') and self.testthread.is_alive():
                self.testthread.join()
            mylogger.debug(f"Threads alive: {threading.activeCount()}")
            # 关闭所有串口
            event.accept()
        else:
            mylogger.debug("Cancel exiting the application")
            event.ignore()

    def getBoxValue(self, box):
        if type(box) == QtWidgets.QDoubleSpinBox:
            return box.value()  ## 获取浮点数据
        elif type(box) == QtWidgets.QSpinBox:
            return box.value()  ## 获取整型数据
        elif type(box) == QtWidgets.QTableWidget:  ## 获取表格数据
            data_table = []
            for i in range(box.rowCount()):
                row_data = []
                for j in range(box.columnCount()):
                    item = box.item(i, j)
                    cell_data = item.text() if item else None
                    row_data.append(cell_data)
                data_table.append(row_data)
            return data_table
        elif type(box) == QtWidgets.QComboBox:  ## 获取下拉框数据
            if box not in self.serialBoxSet:
                return box.currentIndex()  ##返回选中项的索引
            else:
                return box.currentData()  ##返回选中项的数据
        elif type(box) == QtWidgets.QLineEdit:  ## 获取文本框数据
            return box.text()
        elif type(box) == type('str'):  ## 获取字符串数据
            return box
        else:
            raise ValueError("Unknown type of box")

    @log_instance('保存配置文件', mylogger)
    def save_configuration(self):
        new_dict = {name: self.getBoxValue(box) for name, box in self.config_dict.items()}
        # 添加允许测试的等级配置
        new_dict['允许测试的等级'] = self.allowed_test_grades
        mylogger.debug(f"Saving Configuration, dict length: {len(new_dict)}")
        config_file, _ = QFileDialog.getSaveFileName(self, "保存配置文件", "", "Configuration Files (*.json)")
        if config_file:
            if os.path.basename(config_file) == 'logging_config.json':
                raise ValueError("不能保存为logging_config.json, 请更换文件名后重试")

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(new_dict, f, ensure_ascii=False, indent=4)
        ## 保存配置文件

    # 退出时, 自动保存配置文件, 配合closeEvent使用
    def save_configuration_on_exit(self):
        new_dict = {name: self.getBoxValue(box) for name, box in self.config_dict.items()}
        # 添加允许测试的等级配置
        new_dict['允许测试的等级'] = self.allowed_test_grades
        mylogger.debug(f"自动保存配置文件..., dict length: {len(new_dict)}")
        config_file = 'last_config.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(new_dict, f, ensure_ascii=False, indent=4)

    ##向ui控件设置数值
    def setBoxValue(self, box, name, value):
        if type(box) == QtWidgets.QDoubleSpinBox:
            box.setValue(value)  ## 设置浮点数
        elif type(box) == QtWidgets.QSpinBox:
            box.setValue(value)  ## 设置整型数
        elif type(box) == QtWidgets.QTableWidget:
            for i, row in enumerate(value):
                for j, cell in enumerate(row):
                    box.setItem(i, j, QTableWidgetItem(cell))
        elif type(box) == QtWidgets.QComboBox:
            if box not in self.serialBoxSet:
                box.setCurrentIndex(value)
            else:
                self.serial_port_choice[self.serial_port_choice_idx[name]] = value
        elif type(box) == QtWidgets.QLineEdit:
            box.setText(value)
        # elif name == '配置文件路径':
        #     self.config_dict['配置文件路径'] = value
        else:
            raise ValueError("Unknown type of box")

    @log_instance('加载配置文件', mylogger)
    def load_configuration(self):
        config_file, _ = QFileDialog.getOpenFileName(self, "打开配置文件", "", "Configuration Files (*.json)")
        if config_file:
            with open(config_file, 'r', encoding='utf-8') as f:
                saved_dict = json.load(f)
                mylogger.debug(f"Loading Configuration, dict length: {len(saved_dict)}")
                # 不再严格检查配置项数量，因为新增了等级过滤配置
                # if len(saved_dict) != self.configNum:
                #     raise ValueError(f"配置文件格式不匹配")

                for name, box in self.config_dict.items():
                    if name in saved_dict:
                        self.setBoxValue(box, name, saved_dict[name])
                        if box in self.serialBoxSet:
                            mylogger.info(f"设置串口: {name} 为 {saved_dict[name]}")

                # 加载允许测试的等级配置
                if '允许测试的等级' in saved_dict:
                    self.allowed_test_grades = saved_dict['允许测试的等级']
                    mylogger.info(f"加载允许测试的等级: {self.allowed_test_grades}")
                else:
                    # 如果配置文件中没有这个配置项，使用默认值
                    self.allowed_test_grades = ["A", "B", "C", "D"]
                    mylogger.info("使用默认允许测试的等级: A, B, C, D")

                self.updata_serial()
                # 不按照配置文件里的configPath，而是用配置文件的path
                # self.config_dict['配置文件路径'] = config_file
                self.setWindowTitle('NCGK测试系统' + ' 配置: ' + config_file)
        else:
            mylogger.warning("未选择配置文件")

    # 启动时, 自动加载配置文件, 配合__init__使用
    def load_configuration_on_start(self):
        config_file = 'last_config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                saved_dict = json.load(f)
                mylogger.debug(f"自动加载配置文件, dict length: {len(saved_dict)}")
                # 不再严格检查配置项数量，因为新增了等级过滤配置
                # if len(saved_dict) != self.configNum:
                #     raise ValueError(f"配置文件格式不匹配")

                for name, box in self.config_dict.items():
                    if name in saved_dict:
                        self.setBoxValue(box, name, saved_dict[name])
                        if box in self.serialBoxSet:
                            mylogger.info(f"初始化配置，设置串口: {name} 为 {saved_dict[name]}")

                # 加载允许测试的等级配置
                if '允许测试的等级' in saved_dict:
                    self.allowed_test_grades = saved_dict['允许测试的等级']
                    mylogger.info(f"初始化允许测试的等级: {self.allowed_test_grades}")
                else:
                    # 如果配置文件中没有这个配置项，使用默认值
                    self.allowed_test_grades = ["A", "B", "C", "D"]
                    mylogger.info("使用默认允许测试的等级: A, B, C, D")

                self.setWindowTitle('NCGK测试系统' + ' 配置: ' + 'last_config')
        else:
            mylogger.warning("未找到配置文件")

    @log_instance('配置日志输出窗口', mylogger)
    def add_textbrowser_handler_to_logger(self):
        # 创建并配置 QTextBrowserHandler
        editor_handler = QTextBrowserHandler(self.textBrowser)
        editor_handler.setLevel(logging.INFO)

        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%H:%M:%S')
        editor_handler.setFormatter(formatter)

        # 将 texteditor handler 添加到 logger
        mylogger.addHandler(editor_handler)

        # 创建并配置 QMessageBoxHandler
        message_handler = QMessageBoxHandler()
        message_handler.setLevel(logging.ERROR)

        # 设置日志格式
        formatter = logging.Formatter('%(levelname)s - %(message)s')
        message_handler.setFormatter(formatter)

        # 将 message handler 添加到 logger
        mylogger.addHandler(message_handler)

    ##从数据列表中获取指定测试点的数据
    def getTestPoint(self, data, idx):
        offset = 7
        return [data[offset + idx * 5 + x] for x in range(5)] if data[offset + idx * 5] != None else None

    @log_instance('更换测试结果存储路径', mylogger)
    def savedPathButton_clicked(self):
        # 选择文件夹
        path = QFileDialog.getExistingDirectory(self, "选择文件夹", "./")
        if path:
            self.savedPath.setText(path)
            self.resDataPath = path

            # 重新初始化xlsx文件
            mylogger.info("更换测试结果存储路径, 重新初始化xlsx文件...")
            mylogger.warning("请注意: 更换路径后, 之前的测试结果将不可查询, 若需要查看, 请手动转移文件到新路径下")
            self.xlsx_init()
        else:
            mylogger.error("未选择文件夹, 更换路径失败")

    @log_instance('更换数据备份路径', mylogger)
    def backupPathButton_clicked(self):
        # 选择文件夹
        path = QFileDialog.getExistingDirectory(self, "选择文件夹", "./")
        if path:
            self.backupPath.setText(path)
            self.bckDataPath = path

            # 重新初始化xlsx文件
            mylogger.info("更换数据备份路径, 更新配置...")
            self.xlsx_init()
        else:
            mylogger.error("未选择文件夹, 更换路径失败")

    # def search_table_update(self, row, column):
    #     productor_id = self.searchTableWidget.item(row, column).text()
    #     self.search_test_result(productor_id)

    @log_instance('查询测试结果', mylogger)
    def search_test_result(self, productor_id):
        productor_id = productor_id.strip()
        # 遍历所有xlsx文件，查找序列号
        self.tableWidget_3.clearContents()
        self.tableWidget_5.clearContents()
        fileList = os.listdir(self.resDataPath)
        # 按照字典序反向排序，最新的文件在最前面
        fileList.sort(reverse=True)
        found_flag = False

        for file in fileList:
            if file.endswith('.xlsx') and file.startswith('NCGK'):
                mylogger.info(f"正在查找文件: {file}")
                workBook = openpyxl.load_workbook(os.path.join(self.resDataPath, file))
                workSheet = workBook.active

                # 检测文件版本        新加的功能版本号
                file_version = self.detect_excel_file_version(workSheet)
                mylogger.info(f"文件 {file} 版本: {file_version}")

                for cell in workSheet['A']:
                    if str(cell.value) == productor_id and found_flag == False:
                        found_flag = True
                        row = cell.row
                        # 获取对应行的数据，并且填入到tableWidget中
                        data = [cell.value for cell in workSheet[row]]
                        fills = [cell.fill for cell in workSheet[row]]
                        mylogger.debug(f"Found data for ID {productor_id} in file {file}")

                        # 根据文件版本设置数据显示
                        if file_version == "V2.0":
                            # 新版本文件，包含扫码等级
                            self.tableWidget_5.setItem(0, 0,
                                                       QTableWidgetItem(str(data[self.workSheetHeader['测试时间']])))
                            # 显示扫码等级，如果没有则显示空
                            scan_grade = str(data[self.workSheetHeader['扫码等级']]) if len(data) > \
                                                                                        self.workSheetHeader[
                                                                                            '扫码等级'] and data[
                                                                                            self.workSheetHeader[
                                                                                                '扫码等级']] is not None else ''
                            self.tableWidget_5.setItem(0, 1, QTableWidgetItem(scan_grade))
                            self.tableWidget_5.setItem(0, 2,
                                                       QTableWidgetItem(str(data[self.workSheetHeader['是否合格']])))
                            self.tableWidget_5.setItem(0, 3,
                                                       QTableWidgetItem(str(data[self.workSheetHeader['上钳位压力']])))
                            self.tableWidget_5.setItem(0, 4, QTableWidgetItem(
                                str(data[self.workSheetHeader['上钳位测试值']])))
                            self.tableWidget_5.setItem(0, 5,
                                                       QTableWidgetItem(str(data[self.workSheetHeader['下钳位压力']])))
                            self.tableWidget_5.setItem(0, 6, QTableWidgetItem(
                                str(data[self.workSheetHeader['下钳位测试值']])))
                            # 计算测试点数据的起始位置（新版本）
                            test_point_start = 8
                        else:
                            # 旧版本文件，没有扫码等级
                            # 使用旧版本的列索引映射
                            old_header = {'序列号': 0, '测试时间': 1, '是否合格': 2, '上钳位压力': 3, '上钳位测试值': 4,
                                          '下钳位压力': 5, '下钳位测试值': 6}
                            self.tableWidget_5.setItem(0, 0, QTableWidgetItem(str(data[old_header['测试时间']])))
                            self.tableWidget_5.setItem(0, 1, QTableWidgetItem(''))  # 扫码等级为空
                            self.tableWidget_5.setItem(0, 2, QTableWidgetItem(str(data[old_header['是否合格']])))
                            self.tableWidget_5.setItem(0, 3, QTableWidgetItem(str(data[old_header['上钳位压力']])))
                            self.tableWidget_5.setItem(0, 4, QTableWidgetItem(str(data[old_header['上钳位测试值']])))
                            self.tableWidget_5.setItem(0, 5, QTableWidgetItem(str(data[old_header['下钳位压力']])))
                            self.tableWidget_5.setItem(0, 6, QTableWidgetItem(str(data[old_header['下钳位测试值']])))
                            # 计算测试点数据的起始位置（旧版本）
                            test_point_start = 7

                        self.tableWidget_5.viewport().update()
                        # 迭代所有测试点
                        for i in range(int((len(data) - test_point_start) / 5)):
                            testPoint = self.getTestPoint_for_search(data, i, test_point_start)
                            testPointFill = self.getTestPoint_for_search(fills, i, test_point_start)
                            mylogger.debug(f"Test Point {i + 1}: {testPoint}")

                            # 如果表格大小不够, 需要增加新行，设置行的名称
                            if i >= self.tableWidget_3.rowCount():
                                self.tableWidget_3.insertRow(i)
                                self.tableWidget_3.setVerticalHeaderItem(i, QTableWidgetItem(f"测试点{i + 1}"))

                            if testPoint == None:
                                break
                            for j in range(len(testPoint)):
                                self.tableWidget_3.setItem(i, j, QTableWidgetItem(str(testPoint[j])))
                                # 如果不合格，设置背景色为红色
                                # print(f"第{i}个测试点, 第{j}项", testPointFill[j])
                                if testPointFill[j].fgColor.rgb != '00000000':
                                    self.tableWidget_3.item(i, j).setBackground(QtGui.QColor(200, 10, 10))
                            self.tableWidget_3.viewport().update()
                    elif cell.value == productor_id and found_flag == True:
                        mylogger.warning(
                            f"编号: {productor_id}, 存在过去的重复测试结果, 文件名: {file}. 只显示最近的测试结果")
        ##查询功能
        if found_flag == False:
            mylogger.error(f"未找到编号: {productor_id} 的测试结果")
        else:
            mylogger.info(f"查询编号: {productor_id} 的测试结果成功")

    def detect_excel_file_version(self, worksheet):
        """检测Excel文件的版本"""
        try:
            # 检查第一行第一列是否有版本信息
            version_cell = worksheet.cell(1, 1).value
            if version_cell and "版本" in str(version_cell):
                if "V2.0" in str(version_cell):
                    return "V2.0"
                else:
                    return "V2.0"  # 默认为V2.0

            # 检查表头结构来判断版本
            if worksheet.max_row >= 3:
                # 检查第3行（表头行）的列数和内容
                header_row = 3
                headers = []
                for col in range(1, 10):  # 检查前9列
                    cell_value = worksheet.cell(header_row, col).value
                    if cell_value:
                        headers.append(str(cell_value))

                # 如果包含"扫码等级"，则为V2.0版本
                if "扫码等级" in headers:
                    return "V2.0"
                else:
                    return "V1.0"

            return "V1.0"  # 默认为旧版本
        except Exception as e:
            mylogger.error(f"版本检测失败: {e}")
            return "V1.0"  # 出错时默认为旧版本

    def getTestPoint_for_search(self, data, index, start_offset):
        """为查询功能获取测试点数据，支持不同版本的数据格式"""
        try:
            # 计算测试点数据的起始位置
            base_index = start_offset + index * 5
            if base_index + 4 < len(data):
                return [data[base_index], data[base_index + 1], data[base_index + 2], data[base_index + 3],
                        data[base_index + 4]]
            else:
                return None
        except Exception as e:
            mylogger.error(f"获取测试点数据失败: {e}")
            return None

    def refresh_serial_init(self):  ##刷新串口初始化
        self.scan_serial()
        self.updata_serial()
        self.serialinit()

    def tab_change(self, tab_index):  ##切换页面
        mylogger.debug(f"Tab index changed to {tab_index}")
        # 如果切换到测试页面，先清空第一列待测参数，再初始化
        ##清空第一行和第一列的内容
       ## if tab_index==0:   ##切换回设置页面
          ##  self.scan_thread_flag=False
        if tab_index == 1:  # 测试页面的索引
            # 清空第一列以及第一行待测参数

            self.clear_test_parameter_column()
            mylogger.info("切换到测试界面，已清空第一列待测参数")
        self.test_table_para_init()
        self.serialinit()
        # 如果是查询页面，自动更新扫码枪模式
        self.scan_place_update(0, 0, 'Search')
        # 如果切换到测试页面，验证等距模式参数并重置界面状态
        if tab_index == 1:  # 测试页面的索引
            self.validate_interval_mode_parameters()
            # 重置测试界面状态
            self.reset_test_interface_state()

    @log_instance('扫描串口', mylogger)
    def scan_serial(self):
        ports = list(serial.tools.list_ports.comports())
        self.port_list = []
        for port in ports:
            self.port_list.append([port.device, port.description])
            mylogger.info(f"找到串口设备: {port.device} - {port.description}")
        # return port_list

    @log_instance('更新串口数据', mylogger)
    def updata_serial(self):
        self.SerialPressure.clear()
        for port in self.port_list:
            # print(port)
            # print(port[0] + ' ' + port[1])
            self.SerialPressure.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialPressure.itemData(i) == self.serial_port_choice[0]:
                self.SerialPressure.setCurrentIndex(i)
        self.SerialSENT.clear()
        for port in self.port_list:
            self.SerialSENT.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialSENT.itemData(i) == self.serial_port_choice[1]:
                self.SerialSENT.setCurrentIndex(i)
        self.SerialVol.clear()
        for port in self.port_list:
            self.SerialVol.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialVol.itemData(i) == self.serial_port_choice[2]:
                self.SerialVol.setCurrentIndex(i)
        self.Serial8left.clear()
        for port in self.port_list:
            self.Serial8left.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.Serial8left.itemData(i) == self.serial_port_choice[3]:
                self.Serial8left.setCurrentIndex(i)
        self.Serial8right.clear()
        for port in self.port_list:
            self.Serial8right.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.Serial8right.itemData(i) == self.serial_port_choice[4]:
                self.Serial8right.setCurrentIndex(i)
        self.Serial2.clear()
        for port in self.port_list:
            self.Serial2.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.Serial2.itemData(i) == self.serial_port_choice[5]:
                self.Serial2.setCurrentIndex(i)
        self.SerialTempLeft.clear()
        for port in self.port_list:
            self.SerialTempLeft.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialTempLeft.itemData(i) == self.serial_port_choice[6]:
                self.SerialTempLeft.setCurrentIndex(i)
        self.SerialTempRight.clear()
        for port in self.port_list:
            self.SerialTempRight.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialTempRight.itemData(i) == self.serial_port_choice[7]:
                self.SerialTempRight.setCurrentIndex(i)
        self.SerialScan.clear()
        for port in self.port_list:
            self.SerialScan.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialScan.itemData(i) == self.serial_port_choice[8]:
                self.SerialScan.setCurrentIndex(i)
        self.SerialPLC.clear()
        for port in self.port_list:
            self.SerialPLC.addItem(port[0] + ' ' + port[1], userData=port[0])
        for i in range(len(self.port_list)):
            if self.SerialPLC.itemData(i) == self.serial_port_choice[9]:
                self.SerialPLC.setCurrentIndex(i)

        # print(type(self.SerialPressure.currentData()))
        self.serial_list = ['-1'] * 10
        self.serial_baudrate_list = [9600, 115200, 9600, 9600, 9600, 9600, 9600, 9600, 115200, 9600]
        port = None
        if port:
            self.presscontrol = PressureControl(self.port_list[0][0])
            self.relay8left = relay.RelayControl(self.port_list[0][0])
            self.relay8right = relay.RelayControl(self.port_list[0][0])
            self.relay2 = relay.RelayControlTwo(self.port_list[0][0])
            self.templeftread = TempModbus(self.port_list[0][0])
            self.temprightread = TempModbus(self.port_list[0][0])
            self.scanid = scan.IdScan(self.port_list[0][0])
            self.vol = voltage.VolTest(self.port_list[0][0])
            self.plccontrol = plc.PLCModbus(self.port_list[0][0])
            self.sent = SENT(self.port_list[0][0])
        else:
            self.presscontrol = 'PressureControl(self.port_list[0][0])'
            self.relay8left = 'relay.RelayControl(self.port_list[0][0])'
            self.relay8right = 'relay.RelayControl(self.port_list[0][0])'
            self.relay2 = 'relay.RelayControlTwo(self.port_list[0][0])'
            self.templeftread = 'TempModbus(self.port_list[0][0])'
            self.temprightread = 'TempModbus(self.port_list[0][0])'
            self.scanid = 'scan.IdScan(self.port_list[0][0])'
            self.vol = 'voltage.VolTest(self.port_list[0][0])'
            self.plccontrol = 'plc.PLCModbus(self.port_list[0][0])'
            self.sent = 'sent'
        # # 检查是否有可用的串口设备
        # port = len(self.port_list) > 0
        #
        # ###self.port_list = [ ["COM11", 9600],    # [串口号, 波特率]
        #
        # # 初始化硬件设备
        # self.init_hardware_devices()

    def init_hardware_devices(self):
        """初始化硬件设备，包含详细的错误处理"""
        mylogger.info("开始初始化硬件设备...")

        # 检查是否有可用串口
        port_available = len(self.port_list) > 0

        if not port_available:
            mylogger.info("使用模拟设备模式")
            self.init_mock_devices()
            return

        mylogger.debug(f"检测到 {len(self.port_list)} 个串口设备")
        for i, port_info in enumerate(self.port_list):
            mylogger.debug(f"  串口{i}: {port_info[0]} - {port_info[1]}")

        # 尝试初始化各个硬件设备
        hardware_success = True

        try:
            # 压力控制器
            self.presscontrol = PressureControl(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"压力控制器初始化失败: {e}")
            hardware_success = False

        try:
            # 继电器
            self.relay8left = relay.RelayControl(self.port_list[0][0])
            self.relay8right = relay.RelayControl(self.port_list[0][0])
            self.relay2 = relay.RelayControlTwo(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"继电器初始化失败: {e}")
            hardware_success = False

        try:
            # 温度读取器
            self.templeftread = TempModbus(self.port_list[0][0])
            self.temprightread = TempModbus(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"温度读取器初始化失败: {e}")
            hardware_success = False

        try:
            # 扫码枪
            self.scanid = scan.IdScan(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"扫码枪初始化失败: {e}")
            hardware_success = False

        try:
            # 电压表
            self.vol = voltage.VolTest(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"电压表初始化失败: {e}")
            hardware_success = False

        try:
            # PLC控制器
            self.plccontrol = plc.PLCModbus(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"PLC控制器初始化失败: {e}")
            hardware_success = False

        try:
            # SENT通信
            self.sent = SENT(self.port_list[0][0])
        except Exception as e:
            mylogger.debug(f"SENT通信初始化失败: {e}")
            hardware_success = False

        if hardware_success:
            mylogger.info("硬件设备初始化成功")
        else:
            mylogger.info("使用模拟设备模式")
            self.init_mock_devices()

    def init_mock_devices(self):
        """初始化模拟设备对象，用于没有硬件时的测试"""

        class MockDevice:
            def __init__(self, name):
                self.name = name

            def __getattr__(self, item):
                def mock_method(*args, **kwargs):
                    mylogger.debug(f"模拟设备 {self.name} 调用方法 {item}")
                    return None

                return mock_method

            def read_id(self):
                """模拟扫码枪返回测试数据"""
                return "S999999", "TEST"

        # 创建模拟设备对象
        self.presscontrol = MockDevice("压力控制器")
        self.relay8left = MockDevice("左继电器")
        self.relay8right = MockDevice("右继电器")
        self.relay2 = MockDevice("2路继电器")
        self.templeftread = MockDevice("左温度读取器")
        self.temprightread = MockDevice("右温度读取器")
        self.scanid = MockDevice("扫码枪")
        self.vol = MockDevice("电压表")
        self.plccontrol = MockDevice("PLC控制器")
        self.sent = MockDevice("SENT通信")

        mylogger.debug("模拟设备对象创建完成")

    def is_grade_allowed_for_test(self, grade):
        """检查器件等级是否允许进行测试"""
        if not grade or grade.strip() == '':
            # 如果没有等级信息，默认允许测试
            return True

        # 清理等级字符串，去除空格并转换为大写
        clean_grade = grade.strip().upper()

        # 检查是否在允许的等级列表中
        allowed_grades_upper = [g.upper() for g in self.allowed_test_grades]
        is_allowed = clean_grade in allowed_grades_upper

        mylogger.debug(
            f"等级检查: '{grade}' -> '{clean_grade}', 允许的等级: {self.allowed_test_grades}, 结果: {is_allowed}")
        return is_allowed

    def log_grade_filter_summary(self, leftIndicator, rightIndicator):
        """记录等级过滤汇总信息"""
        mylogger.info(f"<font color=blue>等级过滤设置: 允许测试的等级为 {', '.join(self.allowed_test_grades)}</font>")

        # 统计左侧器件等级情况
        left_total = 0
        left_allowed = 0
        left_skipped = 0
        left_details = []

        for j in range(8):
            if leftIndicator[j]:
                left_total += 1
                serial_number = self.tableWidgetLeft.item(0, j + 1).text() if self.tableWidgetLeft.item(0,
                                                                                                        j + 1) is not None else ''
                scan_grade = self.tableWidgetLeft.item(1, j + 1).text() if self.tableWidgetLeft.item(1,
                                                                                                     j + 1) is not None else ''

                if self.is_grade_allowed_for_test(scan_grade):
                    left_allowed += 1
                    left_details.append(f"  第{j + 1}列 {serial_number} (等级: {scan_grade or '无'}) - 允许测试")
                else:
                    left_skipped += 1
                    left_details.append(
                        f"  第{j + 1}列 {serial_number} (等级: {scan_grade or '无'}) - <font color=orange>跳过测试</font>")

        # 统计右侧器件等级情况
        right_total = 0
        right_allowed = 0
        right_skipped = 0
        right_details = []

        for j in range(8):
            if rightIndicator[j]:
                right_total += 1
                serial_number = self.tableWidgetRight.item(0, j + 1).text() if self.tableWidgetRight.item(0,
                                                                                                          j + 1) is not None else ''
                scan_grade = self.tableWidgetRight.item(1, j + 1).text() if self.tableWidgetRight.item(1,
                                                                                                       j + 1) is not None else ''

                if self.is_grade_allowed_for_test(scan_grade):
                    right_allowed += 1
                    right_details.append(f"  第{j + 1}列 {serial_number} (等级: {scan_grade or '无'}) - 允许测试")
                else:
                    right_skipped += 1
                    right_details.append(
                        f"  第{j + 1}列 {serial_number} (等级: {scan_grade or '无'}) - <font color=orange>跳过测试</font>")

        # 输出汇总信息
        total_devices = left_total + right_total
        total_allowed = left_allowed + right_allowed
        total_skipped = left_skipped + right_skipped

        mylogger.info(
            f"<font color=blue>等级过滤汇总: 总器件数 {total_devices}, 允许测试 {total_allowed}, 跳过测试 {total_skipped}</font>")

        if left_total > 0:
            mylogger.info(f"左侧器件 ({left_total}个): 允许测试 {left_allowed}, 跳过 {left_skipped}")
            for detail in left_details:
                mylogger.info(detail)

        if right_total > 0:
            mylogger.info(f"右侧器件 ({right_total}个): 允许测试 {right_allowed}, 跳过 {right_skipped}")
            for detail in right_details:
                mylogger.info(detail)

        if total_skipped > 0:
            mylogger.warning(f"<font color=orange>注意: 有 {total_skipped} 个器件因等级不符合要求而跳过测试</font>")

    def setup_table_click_events(self):
        """设置表格点击事件监听"""
        # 为左侧表格添加点击事件
        self.tableWidgetLeft.cellClicked.connect(self.on_table_cell_clicked)
        # 为右侧表格添加点击事件
        self.tableWidgetRight.cellClicked.connect(self.on_table_cell_clicked)

    def on_table_cell_clicked(self, row, column):
        """表格单元格点击事件处理"""
        # 获取发送信号的表格
        sender = self.sender()

        # 只处理扫码等级行（第1行）的点击
        if row == 1:  # 扫码等级行
            table_name = "Left" if sender == self.tableWidgetLeft else "Right"
            mylogger.info(f"点击了{table_name}侧表格第{column}列的扫码等级单元格")

            # 启动扫码等级读取模式
            self.start_scan_grade_mode(sender, row, column, table_name)

    def start_scan_grade_mode(self, table, row, column, table_name):
        """启动扫码等级读取模式"""
        if self.scan_grade_mode:
            mylogger.warning("已经在扫码等级模式中，请先完成当前扫码")
            return

        # 设置扫码等级模式
        self.scan_grade_mode = True
        self.scan_grade_target = (table, row, column, table_name)

        # 临时设置扫码参数
        self.original_scan_place = self.scan_place
        self.original_scan_column = self.scan_column
        self.scan_place = table_name
        self.scan_column = column

        # 启用扫码
        self.scan_flag = True

        # 更新界面提示
        mylogger.info(f"<font color=blue>请扫描第{column}列器件的等级码...</font>")

        # 高亮显示目标单元格
        self.highlight_target_cell(table, row, column)

    def highlight_target_cell(self, table, row, column):
        """高亮显示目标单元格"""
        # 设置背景色为浅蓝色，表示等待扫码
        table.setItem(row, column, QTableWidgetItem("等待扫码..."))
        if table.item(row, column):
            table.item(row, column).setBackground(QtGui.QColor(173, 216, 230))  # 浅蓝色
            table.viewport().update()

    def handle_scan_grade_result(self, id_str, grade_str):
        """处理扫码等级结果"""
        if not self.scan_grade_target:
            return

        table, row, column, table_name = self.scan_grade_target

        # 使用扫码等级数据，如果没有等级数据则提示错误
        if grade_str:
            result_grade = grade_str
            mylogger.info(f"扫码等级结果: {result_grade}")
        else:
            # 如果没有扫码到等级信息，提示用户
            mylogger.warning("未扫描到等级信息，请确认扫码数据包含ADLMU:等级格式")
            result_grade = "未识别"

        # 设置扫码等级到目标单元格
        table.setItem(row, column, QTableWidgetItem(result_grade))

        # 根据结果设置背景色
        if table.item(row, column):
            if grade_str:
                # 成功获取等级，设置为白色背景
                table.item(row, column).setBackground(QtGui.QColor(255, 255, 255))  # 白色
            else:
                # 未获取到等级，设置为浅红色背景提示
                table.item(row, column).setBackground(QtGui.QColor(255, 200, 200))  # 浅红色
            table.viewport().update()

        # 退出扫码等级模式
        self.exit_scan_grade_mode()

        if grade_str:
            mylogger.info(f"<font color=green>扫码等级设置完成: {result_grade}</font>")
        else:
            mylogger.warning(f"<font color=orange>扫码等级设置失败，请重新扫描包含等级信息的码</font>")

    def exit_scan_grade_mode(self):
        """退出扫码等级模式"""
        if not self.scan_grade_mode:
            return

        # 恢复原始扫码参数
        if hasattr(self, 'original_scan_place'):
            self.scan_place = self.original_scan_place
            self.scan_column = self.original_scan_column

        # 重置扫码等级模式标志
        self.scan_grade_mode = False
        self.scan_grade_target = None

        # 可以选择是否继续扫码（根据需要）
        # self.scan_flag = False  # 如果希望停止扫码

        mylogger.info("退出扫码等级模式")

    def test_table_para_init(self):  ##标准电压值 = 压力值 × 斜率 + 截距
        intercept = self.doubleSpinBox_3.value()  ##截距
        slope = self.doubleSpinBox_4.value()  ##斜率
        ##测试界面
        if self.Model_2.currentIndex():
            pressure_up = self.doubleSpinBox_10.value()  ##上钳位压力上限值
            pressure_down = self.doubleSpinBox_11.value()  ##下钳位压力下限值
            self.tableWidgetLeft.setItem(3, 0, QTableWidgetItem(str(pressure_up) + " kPa"))  # 调整+1
            self.tableWidgetLeft.setItem(4, 0, QTableWidgetItem(str(pressure_down) + " kPa"))  # 调整+1
            self.tableWidgetRight.setItem(3, 0, QTableWidgetItem(str(pressure_up) + " kPa"))  # 调整+1
            self.tableWidgetRight.setItem(4, 0, QTableWidgetItem(str(pressure_down) + " kPa"))  # 调整+1

        # 处理测试点显示
        pressure_list = self.get_pressure_list_for_display()
        for i, pressure_value in enumerate(pressure_list):
            test_standard_value = float(pressure_value) * slope + intercept
            # 在左右表格中显示压力值和标准电压值
            self.tableWidgetLeft.setItem(2 * i + 5, 0, QTableWidgetItem(
                str(pressure_value) + " kPa" + '\n' + "{:.4f} V".format(test_standard_value)))
            self.tableWidgetRight.setItem(2 * i + 5, 0, QTableWidgetItem(
                str(pressure_value) + " kPa" + '\n' + "{:.4f} V".format(test_standard_value)))

        self.tableWidgetLeft.viewport().update()
        self.tableWidgetRight.viewport().update()

    def get_pressure_list_for_display(self):
        """获取用于显示的压力列表，支持压力点模式和压力间隔模式"""
        pressure_list = []

        if self.tabWidget_3.currentIndex() == 0:  # 压力点模式
            for i in range(self.TestPoint.value()):
                if self.tableWidget_2.item(i, 0) is not None:
                    pressure_value = self.tableWidget_2.item(i, 0).text()
                    if pressure_value.strip():  # 确保不是空字符串
                        pressure_list.append(float(pressure_value))
        else:  # 压力间隔模式
            low_pressure = self.Low_Pressure_Value.value()
            high_pressure = self.High_Pressure_Value.value()
            interval_pressure = self.Interval_Pressure.value()

            # 检查参数有效性
            if interval_pressure == 0:
                mylogger.warning('等距模式参数显示：间隔压力值为0，仅显示起始压力值')
                pressure_list.append(low_pressure)
            elif (float(high_pressure) - float(low_pressure)) / float(interval_pressure) <= 0:
                mylogger.warning('等距模式参数显示：压力间隔值符号有误，仅显示起始压力值')
                pressure_list.append(low_pressure)
            else:
                # 计算测试点数量
                pressure_list_length = math.floor(
                    (float(high_pressure) - float(low_pressure)) / float(interval_pressure)) + 1
                # 生成压力列表
                for i in range(pressure_list_length):
                    pressure_value = float(low_pressure) + i * float(interval_pressure)
                    pressure_list.append(pressure_value)

                mylogger.info(f'等距模式显示：计算出{pressure_list_length}个测试点')

        return pressure_list

    def setup_pressure_mode_listeners(self):
        """设置压力模式相关的事件监听"""
        # 监听压力模式切换
        self.tabWidget_3.currentChanged.connect(self.on_pressure_mode_changed)

        # 监听压力间隔模式参数变化
        self.Low_Pressure_Value.valueChanged.connect(self.on_interval_parameters_changed)
        self.High_Pressure_Value.valueChanged.connect(self.on_interval_parameters_changed)
        self.Interval_Pressure.valueChanged.connect(self.on_interval_parameters_changed)

        # 监听测试点数变化（压力点模式）
        self.TestPoint.valueChanged.connect(self.on_test_point_count_changed)

        mylogger.debug("压力模式事件监听器设置完成")

    def on_pressure_mode_changed(self, index):
        """压力模式切换时的处理"""
        if index == 0:
            mylogger.debug("切换到压力点模式")
        else:
            mylogger.debug("切换到压力间隔模式")

        # 更新测试参数显示
        self.test_table_para_init()

    def on_interval_parameters_changed(self):
        """压力间隔模式参数变化时的处理"""
        if self.tabWidget_3.currentIndex() == 1:  # 只在压力间隔模式下响应
            mylogger.debug("压力间隔模式参数发生变化，更新显示")
            self.test_table_para_init()

    def on_test_point_count_changed(self):
        """测试点数变化时的处理"""
        if self.tabWidget_3.currentIndex() == 0:  # 只在压力点模式下响应
            mylogger.debug("测试点数发生变化，更新显示")
            self.test_table_para_init()

    def validate_interval_mode_parameters(self):
        """验证等距模式参数，在切换到测试页面时检查"""
        # 只有在等距模式下才需要验证
        if self.tabWidget_3.currentIndex() == 1:  # 等距模式
            low_pressure = self.Low_Pressure_Value.value()
            high_pressure = self.High_Pressure_Value.value()
            interval_pressure = self.Interval_Pressure.value()

            # 检查间隔压力值是否为0
            if interval_pressure == 0:
                mylogger.warning('等距模式参数错误：间隔压力值不能为0')
                QMessageBox.warning(self, '参数错误',
                                    '等距模式参数错误：\n间隔压力值不能为0，请修改后再进行测试。')
                return False

            # 检查压力范围和间隔的合理性
            try:
                if (float(high_pressure) - float(low_pressure)) / float(interval_pressure) <= 0:
                    mylogger.warning(
                        '等距模式参数错误：压力间隔值符号有误，无法达到设定值，最小值是%f，最大值是%f，间隔是%f' % (
                        low_pressure, high_pressure, interval_pressure))
                    QMessageBox.warning(self, '参数错误',
                                        f'等距模式参数错误：\n压力间隔值符号有误，无法达到设定值\n起始压力：{low_pressure} kPa\n终点压力：{high_pressure} kPa\n间隔压力：{interval_pressure} kPa\n\n请检查参数设置。')
                    return False
            except ZeroDivisionError:
                # 这个情况已经在上面的检查中处理了，但为了安全起见还是保留
                mylogger.warning('等距模式参数错误：间隔压力值不能为0')
                QMessageBox.warning(self, '参数错误',
                                    '等距模式参数错误：\n间隔压力值不能为0，请修改后再进行测试。')
                return False

        return True

    def clean_test_data(self):
        # thread_num = len(threading.enumerate())
        # if thread_num > 2:
        # print("线程数量超过2，说明还在测试中，不再继续运行")
        #    mylogger.debug("线程数量超过2，说明还在测试中，不清空数据")
        #    mylogger.error("正在测试中")
        #    return
        self.tableWidgetLeft.clearContents()
        self.tableWidgetRight.clearContents()
        self.test_table_para_init()

        # 重置计时器和状态
        self.reset_test_timer()
        self.update_test_status("待机中", "#f0f0f0")

        # 重置LED和按钮状态
        self.setLed(self.ledLabel, 'gray', 64)
        self.set_button_in_test(True)

    @log_instance('重新更新串口数据', mylogger)
    def serialinit(self):
        # print("now the tab index is ", tab_index)
        # print("tab change!")
        # print(self.SerialVol.currentData())
        # pass
        mylogger.info('读取串口配置...')
        serial_list_temp = []
        serial_list_temp.append(self.SerialPressure.currentData())
        serial_list_temp.append(self.SerialSENT.currentData())
        serial_list_temp.append(self.SerialVol.currentData())
        serial_list_temp.append(self.Serial8left.currentData())
        serial_list_temp.append(self.Serial8right.currentData())
        serial_list_temp.append(self.Serial2.currentData())
        serial_list_temp.append(self.SerialTempLeft.currentData())
        serial_list_temp.append(self.SerialTempRight.currentData())
        serial_list_temp.append(self.SerialScan.currentData())
        serial_list_temp.append(self.SerialPLC.currentData())
        mylogger.info('读取串口波特率配置...')
        serial_baudrate_list_temp = []
        serial_baudrate_list_temp.append(self.spinBox_3.value())
        serial_baudrate_list_temp.append(self.spinBox_4.value())
        serial_baudrate_list_temp.append(self.spinBox_5.value())
        serial_baudrate_list_temp.append(self.spinBox_6.value())
        serial_baudrate_list_temp.append(self.spinBox_7.value())
        serial_baudrate_list_temp.append(self.spinBox_8.value())
        serial_baudrate_list_temp.append(self.spinBox_9.value())
        serial_baudrate_list_temp.append(self.spinBox_10.value())
        serial_baudrate_list_temp.append(self.spinBox_11.value())
        serial_baudrate_list_temp.append(self.spinBox_12.value())

        # if serial_list_temp != self.serial_list or serial_baudrate_list_temp != self.serial_baudrate_list:
        #     for serial_num, serial_baudrate, serial_num_temp, serial_baudrate_temp in \
        #             self.serial_list, self.serial_baudrate_list, serial_list_temp, serial_baudrate_list_temp:
        #         if serial_num_temp != serial_num or serial_baudrate_temp != serial_baudrate:
        mylogger.info('更新串口...')
        if serial_list_temp != self.serial_list or serial_baudrate_list_temp != self.serial_baudrate_list:
            if self.serial_list[0] != serial_list_temp[0] or self.serial_baudrate_list[0] != serial_baudrate_list_temp[
                0]:
                if self.presscontrol:
                    del self.presscontrol
                self.presscontrol = PressureControl(serial_list_temp[0], serial_baudrate_list_temp[0])
            if self.serial_list[1] != serial_list_temp[1] or self.serial_baudrate_list[1] != serial_baudrate_list_temp[
                1]:
                if self.sent:
                    del self.sent
                self.sent = SENT(serial_list_temp[1], serial_baudrate_list_temp[1])
                self.sent.channel1_start()
            if self.serial_list[2] != serial_list_temp[2] or self.serial_baudrate_list[2] != serial_baudrate_list_temp[
                2]:
                if self.vol:
                    del self.vol
                self.vol = voltage.VolTest(serial_list_temp[2], serial_baudrate_list_temp[2])
            if self.serial_list[3] != serial_list_temp[3] or self.serial_baudrate_list[3] != serial_baudrate_list_temp[
                3]:
                if self.relay8left:
                    del self.relay8left
                self.relay8left = relay.RelayControl(serial_list_temp[3], serial_baudrate_list_temp[3])
            if self.serial_list[4] != serial_list_temp[4] or self.serial_baudrate_list[4] != serial_baudrate_list_temp[
                4]:
                if self.relay8right:
                    del self.relay8right
                self.relay8right = relay.RelayControl(serial_list_temp[4], serial_baudrate_list_temp[4])
            if self.serial_list[5] != serial_list_temp[5] or self.serial_baudrate_list[5] != serial_baudrate_list_temp[
                5]:
                if self.relay2:
                    del self.relay2
                self.relay2 = relay.RelayControlTwo(serial_list_temp[5], serial_baudrate_list_temp[5])
            if self.serial_list[6] != serial_list_temp[6] or self.serial_baudrate_list[6] != serial_baudrate_list_temp[
                6]:
                if self.templeftread:
                    del self.templeftread
                self.templeftread = TempModbus(serial_list_temp[6], serial_baudrate_list_temp[6])
            if self.serial_list[7] != serial_list_temp[7] or self.serial_baudrate_list[7] != serial_baudrate_list_temp[
                7]:
                if self.temprightread:
                    del self.temprightread
                self.temprightread = TempModbus(serial_list_temp[7], serial_baudrate_list_temp[7])
            if self.serial_list[8] != serial_list_temp[8] or self.serial_baudrate_list[8] != serial_baudrate_list_temp[
                8]:
                if self.scanid:
                    del self.scanid
                self.scanid = scan.IdScan(serial_list_temp[8], serial_baudrate_list_temp[8])

                if self.scanthread.is_alive():
                    self.scan_thread_flag = False
                    self.scanthread.join()
                self.scan_flag = True
                self.scan_thread_flag = True
                self.scanthread = threading.Thread(target=self.scan_thread)
                self.scanthread.start()
            if self.serial_list[9] != serial_list_temp[9] or self.serial_baudrate_list[9] != serial_baudrate_list_temp[
                9]:
                if self.plccontrol:
                    del self.plccontrol
                print(serial_list_temp[9], serial_baudrate_list_temp[9])
                self.plccontrol = plc.PLCModbus(serial_list_temp[9], serial_baudrate_list_temp[9])
            self.serial_list = serial_list_temp
            self.serial_baudrate_list = serial_baudrate_list_temp

    def start_test_thread(self):
        mylogger.debug("Call: start_test_thread()")
        thread_num = len(threading.enumerate())
        print("thread_num is: ", thread_num)
        mylogger.debug(f"thread_num is: {thread_num}")
        # if thread_num > 3:
        #     print("线程数量超过3，说明还在测试中，不再继续运行")
        #     return

        # 立即启动计时器和更新状态
        self.start_test_timer()
        self.update_test_status("测试中", "#d4edda")

        self.testthread = threading.Thread(target=self.start_test)
        self.test_thread_flag = True
        self.testthread.start()

    def set_button_in_test(self, flag: bool):
        self.pushButton_2.setEnabled(flag)
        self.pushButton_5.setEnabled(flag)
        self.tabWidget.setTabEnabled(0, flag)
        self.tabWidget.setTabEnabled(2, flag)

    def _float_conversion(self, number, type):
        if type == 'normal':
            return f"{number:.4f}"
        else:
            return f"{number * 100:.4f}"

    # 子线程通过信号与槽机制使主线程弹框, 使用方法: self.show_dialog.emit('title', 'message'), 然后self.testSem.acquire()等待结果self.testDialogResult
    def _show_dialog(self, title, message):
        reply = QMessageBox.question(self, title, message,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

        self.testDialogResult = reply
        self.testSem.release()

    def _child_show_dialog(self, title, message):
        mylogger.warning(f'{title}')
        self.testDialogSignal.emit(title, message)
        self.testSem.acquire()
        if self.testDialogResult == QMessageBox.Yes:
            mylogger.debug(f'{message}, 用户选择继续测试')
            self.setLed(self.ledLabel, 'yellow', 64)
            return True
        else:
            mylogger.debug(f'{message}, 用户选择停止测试')
            self.reset_test_config()
            return False

    @log_instance('正在测试', mylogger)
    def start_test(self):
        # 计时器已在start_test_thread中启动，这里只需要设置其他状态
        self.setLed(self.ledLabel, 'green', 64)
        self.set_button_in_test(False)
        # self.tableWidgetLeft.clearContents()
        # self.tableWidgetRight.clearContents()
        self.scan_flag = False
        i = 0
        leftIndicator = []
        for i in range(9):
            # print(self.tableWidgetLeft.item(0, i).text())
            if self.tableWidgetLeft.item(0, i + 1) is None or self.tableWidgetLeft.item(0, i + 1).text() == "":
                leftIndicator.append(False)
            else:
                leftIndicator.append(True)
        left_num = sum(leftIndicator)
        mylogger.info(f"左侧器件数量: {left_num}")

        rightIndicator = []
        for i in range(9):
            # print(self.tableWidgetRight.item(0, i).text())
            if self.tableWidgetRight.item(0, i + 1) is None or self.tableWidgetRight.item(0, i + 1).text() == "":
                rightIndicator.append(False)
            else:
                rightIndicator.append(True)
        right_num = sum(rightIndicator)
        mylogger.info(f"右侧器件数量: {right_num}")

        # 记录测试设备总数
        total_devices = left_num + right_num

        # 检查并记录等级过滤情况
        self.log_grade_filter_summary(leftIndicator, rightIndicator)

        test_list = []
        # 注意这里test_list的大小是固定的, 根据首位是否有数据来判断是否有器件，然后通过tableWidget来传递值。
        # 新格式：[序列号, 测试时间, 扫码等级, 是否合格, 上钳位压力, 上钳位测试值, 下钳位压力, 下钳位测试值, ...]
        for i in range(8):
            if self.tableWidgetLeft.item(0, i + 1) is not None:
                # 获取序列号和扫码等级
                serial_number = self.tableWidgetLeft.item(0, i + 1).text()
                # 从扫码枪获取的扫码等级
                scan_grade = self.tableWidgetLeft.item(1, i + 1).text() if self.tableWidgetLeft.item(1,
                                                                                                     i + 1) is not None else ''
                test_list.append([serial_number, '', scan_grade, '合格', '', '', '', ''])
            else:
                test_list.append(['', '', '', '合格', '', '', '', ''])
        for i in range(8):
            if self.tableWidgetRight.item(0, i + 1) is not None:
                # 获取序列号和扫码等级
                serial_number = self.tableWidgetRight.item(0, i + 1).text()
                # 从扫码枪获取的扫码等级
                scan_grade = self.tableWidgetRight.item(1, i + 1).text() if self.tableWidgetRight.item(1,
                                                                                                       i + 1) is not None else ''
                test_list.append([serial_number, '', scan_grade, '合格', '', '', '', ''])
            else:
                test_list.append(['', '', '', '合格', '', '', '', ''])
        mode = self.Model.currentIndex()
        intercept = self.doubleSpinBox_3.value()
        slope = self.doubleSpinBox_4.value()
        temperature_left = self.TempLeft.value()
        temperature_right = self.TempRight.value()
        temperature_error_range = self.doubleSpinBox_2.value()
        low_temp_error_range_down = self.doubleSpinBox_5.value() / 100
        mid_temp_error_range_down = self.doubleSpinBox_6.value() / 100
        high_temp_error_range_down = self.doubleSpinBox_7.value() / 100
        low_temp_error_range_up = self.doubleSpinBox_31.value() / 100
        mid_temp_error_range_up = self.doubleSpinBox_32.value() / 100
        high_temp_error_range_up = self.doubleSpinBox_33.value() / 100
        error_standard_vol = self.doubleSpinBox_16.value()
        low_mid_temp = self.doubleSpinBox_8.value()
        mid_high_temp = self.doubleSpinBox_9.value()
        try:
            mylogger.info('正在尝试稳定温度...')
            if left_num > 0:
                self.templeftread.temp_stable(temperature_left, temperature_error_range)
            if right_num > 0:
                self.temprightread.temp_stable(temperature_right, temperature_error_range)
        except TemperatureError as e:
            if not self._child_show_dialog('温度稳定失败, 是否继续', f'{e}'):
                return
        # 压力测试开始，由于有两种模式，在两种模式下，把不同的值都设置到pressure_list中，然后再循环。
        pressure_list = []
        pressure_list_length = 0
        if self.tabWidget_3.currentIndex() == 0:
            pressure_list_length = self.TestPoint.value()
            mylogger.info('压力点测试点数是%d' % (pressure_list_length))
            for i in range(pressure_list_length):
                pressure_list.append(self.tableWidget_2.item(i, 0).text())
                mylogger.info('第%d个压力点测试值是%f' % (i + 1, float(pressure_list[i])))
        else:
            low_pressure = self.Low_Pressure_Value.value()
            high_pressure = self.High_Pressure_Value.value()
            interval_pressure = self.Interval_Pressure.value()

            # 检查间隔压力值是否为0，避免除零错误
            if interval_pressure == 0:
                mylogger.error('等距模式参数错误：间隔压力值不能为0，测试终止')
                if not self._child_show_dialog('等距模式参数错误',
                                               '间隔压力值不能为0，是否继续测试（将使用起始压力值作为唯一测试点）'):
                    return
                pressure_list_length = 1
                pressure_list.append(low_pressure)
                mylogger.info('压力点测试点数是%d' % (pressure_list_length))
                mylogger.info('第%d个压力点测试值是%f' % (1, pressure_list[0]))
            elif (float(high_pressure) - float(low_pressure)) / float(interval_pressure) <= 0:
                mylogger.warning('压力间隔模式的间隔值符号有误，无法达到设定值，最小值是%f，最大值是%f，间隔是%f' % (
                low_pressure, high_pressure, interval_pressure))
                if not self._child_show_dialog('压力间隔模式值填写错误，无合适值', '是否继续测试'):
                    return
                pressure_list_length = 1
                pressure_list.append(low_pressure)
                mylogger.info('压力点测试点数是%d' % (pressure_list_length))
                mylogger.info('第%d个压力点测试值是%f' % (1, pressure_list[0]))
            else:
                pressure_list_length = math.floor(
                    (float(high_pressure) - float(low_pressure)) / float(interval_pressure)) + 1
                mylogger.info('压力点测试点数是%d' % (pressure_list_length))
                for i in range(pressure_list_length):
                    pressure_list.append(float(low_pressure) + i * float(interval_pressure))
                    mylogger.info('第%d个压力点测试值是%f' % (i + 1, pressure_list[i]))

        for i in range(pressure_list_length):
            if not self.test_thread_flag:
                return

            mylogger.info('---------------------开始测试第%d个点---------------------' % (i + 1))
            pressure_value = pressure_list[i]

            mylogger.info('--开始进行正负压切换--')
            try:
                if left_num > 0:  # 获得压力值后，判断是低压还是高压
                    mylogger.info('分析左侧器件')
                    # print("float(pressure_value)", float(pressure_value))
                    if float(pressure_value) < 0:
                        mylogger.info('设定值为负压..')
                        if not self.plccontrol.plc_assert('left', 1):
                            mylogger.info('PLC控制压力源为负压')
                            self.presscontrol.vent()
                            self.plccontrol.plc_assert_action('left', 1)
                        else:
                            mylogger.info('压力源已经是负压了')
                    else:
                        mylogger.info('设定值为正压..')
                        if not self.plccontrol.plc_assert('left', 2):
                            mylogger.info('PLC控制压力源为正压')
                            self.presscontrol.vent()
                            self.plccontrol.plc_assert_action('left', 2)
                        else:
                            mylogger.info('压力源已经是正压了')
                if right_num > 0:
                    mylogger.info('分析右侧器件')
                    if float(pressure_value) < 0:
                        mylogger.info('设定值为负压..')
                        # print("self.plccontrol.plc_assert('left', 1)", self.plccontrol.plc_assert('left', 1))
                        if not self.plccontrol.plc_assert('right', 1):
                            mylogger.info('PLC控制压力源为负压')
                            self.presscontrol.vent()
                            self.plccontrol.plc_assert_action('right', 1)
                        else:
                            mylogger.info('压力源已经是负压了')
                    else:
                        mylogger.info('设定值为正压..')
                        # print("self.plccontrol.plc_assert('left', 2)", self.plccontrol.plc_assert('left', 2))
                        if not self.plccontrol.plc_assert('right', 2):
                            mylogger.info('PLC控制压力源为正压')
                            self.presscontrol.vent()
                            self.plccontrol.plc_assert_action('right', 2)
                        else:
                            mylogger.info('压力源已经是正压了')
            except PressureSourceError as e:
                if not self._child_show_dialog('压力源切换失败, 是否继续', f'{e}'):
                    return
            except PlcError as e:
                if not self._child_show_dialog('PLC控制失败, 是否继续', f'{e}'):
                    return

            if not self.test_thread_flag:
                return

            try:
                self.presscontrol.cont()
                mylogger.info('正在尝试稳定压力...')
                self.presscontrol.set_still_pressure(pressure_value)
            except PressureSourceError as e:
                if not self._child_show_dialog('压力稳定失败, 是否继续', f'{e}'):
                    return

            # 测试左路，根据模拟与数据选择。
            mylogger.info('--开始测试左路--')
            try:
                self.relay2.testchannel(mode, 1)
            except RelayError as e:
                if not self._child_show_dialog('继电器控制失败, 是否继续', f'{e}'):
                    return

            for j in range(8):
                if not self.test_thread_flag:
                    return

                try:
                    if not leftIndicator[j]:
                        continue

                    # 检查器件等级是否允许测试
                    scan_grade = self.tableWidgetLeft.item(1, j + 1).text() if self.tableWidgetLeft.item(1,
                                                                                                         j + 1) is not None else ''
                    if not self.is_grade_allowed_for_test(scan_grade):
                        serial_number = self.tableWidgetLeft.item(0, j + 1).text() if self.tableWidgetLeft.item(0,
                                                                                                                j + 1) is not None else ''
                        mylogger.warning(
                            f"<font color=orange>左侧第{j + 1}列器件 {serial_number} 等级 '{scan_grade}' 不在允许测试范围内，跳过测试</font>")

                        # 在界面上标记为跳过测试
                        test_list[j][3] = '不合格'
                        self.tableWidgetLeft.item(0, j + 1).setBackground(
                                        QtGui.QColor(200, 10, 10))  # 红色背景
                        for row_offset in range(5 + i * 2, 7 + i * 2):  # 测试值和误差行
                            if row_offset < self.tableWidgetLeft.rowCount():
                                self.tableWidgetLeft.setItem(row_offset, j + 1, QTableWidgetItem("跳过"))
                                if self.tableWidgetLeft.item(row_offset, j + 1):
                                    self.tableWidgetLeft.item(row_offset, j + 1).setBackground(
                                        QtGui.QColor(200, 200, 200))  # 灰色背景
                        self.tableWidgetLeft.viewport().update()
                        continue
                    if i == 0:
                        time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        test_list[j][1] = time_now
                    self.relay8left.openchannel(j + 1)
                    time.sleep(0.5)
                    test_data = 32
                    if (mode == 0):
                        mylogger.info('读取模拟电压中...')
                        test_data = self.vol.read_vol_avg()
                        mylogger.info(f"读取到的模拟电压为: {test_data} V")
                    else:
                        mylogger.info('读取SENT数据...')
                        test_data = self.sent.read_channel1_data()
                        mylogger.info(f"读取到的SENT数据为: {test_data}")
                    # 注意用什么方式获取
                    # test_pressure = test_data * slope + intercept
                    ref_test_data = float(pressure_value) * slope + intercept
                    # ref_test_data = float(self.tableWidgetLeft.item(2 * i + 3, 0).text())
                    error = (test_data - ref_test_data) / error_standard_vol
                    self.tableWidgetLeft.setItem(5 + i * 2, j + 1,
                                                 QTableWidgetItem(self._float_conversion(test_data, "normal")))  # 调整+1
                    self.tableWidgetLeft.setItem(6 + i * 2, j + 1, QTableWidgetItem(
                        self._float_conversion(error, "percent") + '%'))  # 调整+1
                    ## 用数值类型保存数据
                    test_list[j].append(temperature_left)
                    test_list[j].append(pressure_value)
                    test_list[j].append(test_data)
                    test_list[j].append(ref_test_data)
                    test_list[j].append(error)
                    self.tableWidgetLeft.viewport().update()

                    if temperature_left > mid_high_temp:  # 高温
                        mylogger.info('左侧检测到高温')
                        if error > high_temp_error_range_up or error < high_temp_error_range_down:
                            mylogger.warning('左侧高温下不合格')
                            self.tableWidgetLeft.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetLeft.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                    elif temperature_left > low_mid_temp:  # 常温
                        mylogger.info('左侧检测到常温')
                        if error > mid_temp_error_range_up or error < mid_temp_error_range_down:
                            mylogger.warning('左侧常温下不合格')
                            self.tableWidgetLeft.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetLeft.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                    else:
                        mylogger.info('左侧检测到低温')
                        if error > low_temp_error_range_up or error < low_temp_error_range_down:
                            mylogger.warning('左侧低温下不合格')
                            self.tableWidgetLeft.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetLeft.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                except VoltageError as e:
                    if not self._child_show_dialog('模拟电压读取失败, 是否继续', f'{e}'):
                        return
                except RelayError as e:
                    if not self._child_show_dialog('继电器控制失败, 是否继续', f'{e}'):
                        return
                except SENTError as e:
                    if not self._child_show_dialog('SENT数据读取失败, 是否继续', f'{e}'):
                        return

            try:
                self.relay8left.closeall()
            except RelayError as e:
                if not self._child_show_dialog('左路继电器关闭失败, 是否继续', f'{e}'):
                    return

            mylogger.info('--开始测试右路--')
            self.relay2.testchannel(mode, 2)
            for j in range(8):
                if not self.test_thread_flag:
                    return

                try:
                    if not rightIndicator[j]:
                        continue

                    # 检查器件等级是否允许测试
                    scan_grade = self.tableWidgetRight.item(1, j + 1).text() if self.tableWidgetRight.item(1,
                                                                                                           j + 1) is not None else ''
                    if not self.is_grade_allowed_for_test(scan_grade):
                        serial_number = self.tableWidgetRight.item(0, j + 1).text() if self.tableWidgetRight.item(0,
                                                                                                                  j + 1) is not None else ''
                        mylogger.warning(
                            f"<font color=orange>右侧第{j + 1}列器件 {serial_number} 等级 '{scan_grade}' 不在允许测试范围内，跳过测试</font>")
                        # 在界面上标记为跳过测试
                        ##跳过测试设置为不合格，且标红序列号，其他行标为灰色
                        test_list[j][3] = '不合格'
                        self.tableWidgetRight.item(0, j + 1).setBackground(
                                        QtGui.QColor(200, 10, 10))  # 红色背景
                        for row_offset in range(5 + i * 2, 7 + i * 2):  # 测试值和误差行
                            if row_offset < self.tableWidgetRight.rowCount():
                                self.tableWidgetRight.setItem(row_offset, j + 1, QTableWidgetItem("跳过"))
                                if self.tableWidgetRight.item(row_offset, j + 1):
                                    self.tableWidgetRight.item(row_offset, j + 1).setBackground(
                                        QtGui.QColor(200, 200, 200))  # 灰色背景
                        self.tableWidgetRight.viewport().update()
                        continue
                    if i == 0:
                        time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        test_list[j + 8][1] = time_now
                    self.relay8right.openchannel(j + 1)
                    time.sleep(0.5)
                    test_data = float(0.5)
                    if (mode == 0):
                        mylogger.info('读取模拟电压中...')
                        test_data = self.vol.read_vol_avg()
                        mylogger.info(f"读取到的模拟电压为: {test_data} V")
                    else:
                        mylogger.info('读取SENT数据中...')
                        test_data = self.sent.read_channel1_data()
                        mylogger.info(f"读取到的SENT数据为: {test_data}")
                    # 注意用什么方式获取
                    # ref_test_data = float(self.tableWidgetLeft.item(2 * i + 3, 0).text())
                    ref_test_data = float(pressure_value) * slope + intercept
                    error = (test_data - ref_test_data) / error_standard_vol
                    self.tableWidgetRight.setItem(5 + i * 2, j + 1,
                                                  QTableWidgetItem(self._float_conversion(test_data, "normal")))  # 调整+1
                    self.tableWidgetRight.setItem(6 + i * 2, j + 1, QTableWidgetItem(
                        self._float_conversion(error, "percent") + '%'))  # 调整+1
                    test_list[j + 8].append(temperature_right)
                    test_list[j + 8].append(pressure_value)
                    test_list[j + 8].append(test_data)
                    test_list[j + 8].append(ref_test_data)
                    test_list[j + 8].append(error)
                    self.tableWidgetRight.viewport().update()

                    if temperature_right > mid_high_temp:  # 高温
                        mylogger.info('右侧检测到高温')
                        if error > high_temp_error_range_up or error < high_temp_error_range_down:
                            mylogger.warning('右侧高温下不合格')
                            self.tableWidgetRight.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j + 8][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetRight.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                    elif temperature_right > low_mid_temp:  # 常温
                        mylogger.info('右侧检测到常温')
                        if error > mid_temp_error_range_up or error < mid_temp_error_range_down:
                            mylogger.warning('右侧常温下不合格')
                            self.tableWidgetRight.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j + 8][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetRight.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                    else:
                        mylogger.info('右侧检测到低温')
                        if error > low_temp_error_range_up or error < low_temp_error_range_down:
                            mylogger.warning('右侧低温下不合格')
                            self.tableWidgetRight.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j + 8][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetRight.item(5 + i * 2, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                except VoltageError as e:
                    if not self._child_show_dialog('模拟电压读取失败, 是否继续', f'{e}'):
                        return
                except RelayError as e:
                    if not self._child_show_dialog('继电器控制失败, 是否继续', f'{e}'):
                        return
                except SENTError as e:
                    if not self._child_show_dialog('SENT数据读取失败, 是否继续', f'{e}'):
                        return

            try:
                self.relay8right.closeall()
            except RelayError as e:
                if not self._child_show_dialog('右路继电器关闭失败, 是否继续', f'{e}'):
                    return

        if self.Model_2.currentIndex():  # 如果选中要测试钳位电压的话
            if not self.test_thread_flag:
                return

            mylogger.info('---------------------开始测试钳位电压---------------------')
            pressure_value_list = [self.doubleSpinBox_10.value(), self.doubleSpinBox_11.value()]
            pressure_down_vols = [self.doubleSpinBox_12.value(), self.doubleSpinBox_15.value()]
            pressure_up_vols = [self.doubleSpinBox_13.value(), self.doubleSpinBox_14.value()]
            for pressure_value, down_vol, up_vol, i in zip(pressure_value_list, pressure_down_vols, pressure_up_vols,
                                                           range(2)):
                if i == 0:
                    mylogger.info('上钳位...')
                else:
                    mylogger.info('下钳位...')

                if not self.test_thread_flag:
                    return

                try:
                    mylogger.info('--开始进行正负压切换--')
                    if left_num > 0:  # 获得压力值后，判断是低压还是高压
                        mylogger.info('分析左侧')
                        if float(pressure_value) < 0:
                            mylogger.info('设定值为负压..')
                            if not self.plccontrol.plc_assert('left', 1):
                                mylogger.info('PLC控制压力源为负压')
                                self.presscontrol.vent()
                                self.plccontrol.plc_assert_action('left', 1)
                            else:
                                mylogger.info('压力源已经是负压了')
                        else:
                            mylogger.info('设定值为正压..')
                            if not self.plccontrol.plc_assert('left', 2):
                                mylogger.info('PLC控制压力源为正压')
                                self.presscontrol.vent()
                                self.plccontrol.plc_assert_action('left', 2)
                            else:
                                mylogger.info('压力源已经是正压了')
                    if right_num > 0:
                        mylogger.info('分析右侧')
                        if float(pressure_value) < 0:
                            mylogger.info('设定值为负压..')
                            if not self.plccontrol.plc_assert('right', 1):
                                mylogger.info('PLC控制压力源为负压')
                                self.presscontrol.vent()
                                self.plccontrol.plc_assert_action('right', 1)
                            else:
                                mylogger.info('压力源已经是负压了')
                        else:
                            mylogger.info('设定值为正压..')
                            if not self.plccontrol.plc_assert('right', 2):
                                mylogger.info('PLC控制压力源为正压')
                                self.presscontrol.vent()
                                self.plccontrol.plc_assert_action('right', 2)
                            else:
                                mylogger.info('压力源已经是正压了')
                except PressureSourceError as e:
                    if not self._child_show_dialog('压力源切换失败, 是否继续', f'{e}'):
                        return
                except PlcError as e:
                    if not self._child_show_dialog('PLC控制失败, 是否继续', f'{e}'):
                        return

                if not self.test_thread_flag:
                    return

                try:
                    self.presscontrol.cont()
                    mylogger.info('正在尝试稳定压力...')
                    self.presscontrol.set_still_pressure(pressure_value)
                    # mylogger.info('--开始测试左路--')
                    # self.relay2.testchannel(mode, 1)
                except PressureSourceError as e:
                    if not self._child_show_dialog('压力稳定失败, 是否继续', f'{e}'):
                        return
                try:
                    mylogger.info('--开始测试左路--')
                    self.relay2.testchannel(mode, 1)
                except RelayError as e:
                    if not self._child_show_dialog('继电器控制失败, 是否继续', f'{e}'):
                        return

                for j in range(8):
                    if not self.test_thread_flag:
                        return

                    try:
                        if not leftIndicator[j]:
                            continue

                        # 检查器件等级是否允许测试
                        scan_grade = self.tableWidgetLeft.item(1, j + 1).text() if self.tableWidgetLeft.item(1,
                                                                                                             j + 1) is not None else ''
                        if not self.is_grade_allowed_for_test(scan_grade):
                            serial_number = self.tableWidgetLeft.item(0, j + 1).text() if self.tableWidgetLeft.item(0,
                                                                                                                    j + 1) is not None else ''
                            mylogger.warning(
                                f"<font color=orange>左侧第{j + 1}列器件 {serial_number} 等级 '{scan_grade}' 不在允许测试范围内，跳过钳位测试</font>")
                            # 在界面上标记为跳过测试
                            self.tableWidgetLeft.setItem(2, j + 1, QTableWidgetItem("不合格"))
                            self.tableWidgetLeft.setItem(3 + i, j + 1, QTableWidgetItem("跳过"))
                            test_list[j][3] = '不合格'
                            if self.tableWidgetLeft.item(3 + i, j + 1):
                                self.tableWidgetLeft.item(3 + i, j + 1).setBackground(
                                    QtGui.QColor(200, 200, 200))  # 灰色背景
                            self.tableWidgetLeft.viewport().update()
                            continue

                        self.relay8left.openchannel(j + 1)
                        time.sleep(0.5)
                        test_data = 32
                        if mode == 0:
                            mylogger.info('读取模拟电压中...')
                            test_data = self.vol.read_vol_avg()
                            mylogger.info(f"读取到的模拟电压为: {test_data} V")
                        else:
                            mylogger.info('读取SENT数据中...')
                            test_data = self.sent.read_channel1_data()
                            mylogger.info(f"读取到的SENT数据为: {test_data}")
                        self.tableWidgetLeft.setItem(3 + i, j + 1, QTableWidgetItem(
                            self._float_conversion(test_data, "normal")))  # 调整+1
                        test_list[j][4 + 2 * i] = pressure_value  # 调整索引+1
                        test_list[j][5 + 2 * i] = test_data  # 调整索引+1
                        if test_data > up_vol or test_data < down_vol:
                            self.tableWidgetLeft.item(3 + i, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetLeft.item(3 + i, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                        self.tableWidgetLeft.viewport().update()
                    except VoltageError as e:
                        if not self._child_show_dialog('模拟电压读取失败, 是否继续', f'{e}'):
                            return
                    except RelayError as e:
                        if not self._child_show_dialog('继电器控制失败, 是否继续', f'{e}'):
                            return
                    except SENTError as e:
                        if not self._child_show_dialog('SENT数据读取失败, 是否继续', f'{e}'):
                            return
                try:
                    self.relay8left.closeall()
                    mylogger.info('--开始测试右路--')
                    self.relay2.testchannel(mode, 2)
                except RelayError as e:
                    if not self._child_show_dialog('继电器控制失败, 是否继续', f'{e}'):
                        return

                for j in range(8):
                    if not self.test_thread_flag:
                        return

                    try:
                        if not rightIndicator[j]:
                            continue

                        # 检查器件等级是否允许测试
                        scan_grade = self.tableWidgetRight.item(1, j + 1).text() if self.tableWidgetRight.item(1,
                                                                                                               j + 1) is not None else ''
                        if not self.is_grade_allowed_for_test(scan_grade):
                            serial_number = self.tableWidgetRight.item(0, j + 1).text() if self.tableWidgetRight.item(0,
                                                                                                                      j + 1) is not None else ''
                            mylogger.warning(
                                f"<font color=orange>右侧第{j + 1}列器件 {serial_number} 等级 '{scan_grade}' 不在允许测试范围内，跳过钳位测试</font>")
                            # 在界面上标记为跳过测试
                            self.tableWidgetRight.setItem(3 + i, j + 1, QTableWidgetItem("跳过"))
                            self.tableWidgetRight.setItem(2, j + 1, QTableWidgetItem("不合格"))
                            test_list[j + 8][3] = '不合格'
                            if self.tableWidgetRight.item(3 + i, j + 1):
                                self.tableWidgetRight.item(3 + i, j + 1).setBackground(
                                    QtGui.QColor(200, 200, 200))  # 灰色背景
                            self.tableWidgetRight.viewport().update()
                            continue

                        self.relay8right.openchannel(j + 1)
                        time.sleep(0.5)
                        test_data = 32
                        if mode == 0:
                            mylogger.info('读取模拟电压中...')
                            test_data = self.vol.read_vol_avg()
                            mylogger.info(f"读取到的模拟电压为: {test_data} V")
                        else:
                            mylogger.info('读取SENT数据中...')
                            test_data = self.sent.read_channel1_data()
                            mylogger.info(f"读取到的SENT数据为: {test_data}")
                        self.tableWidgetRight.setItem(3 + i, j + 1, QTableWidgetItem(
                            self._float_conversion(test_data, "normal")))  # 调整+1
                        test_list[j + 8][4 + 2 * i] = pressure_value  # 调整索引+1
                        test_list[j + 8][5 + 2 * i] = test_data  # 调整索引+1
                        if test_data > up_vol or test_data < down_vol:
                            self.tableWidgetRight.item(3 + i, j + 1).setBackground(
                                QtGui.QColor(200, 10, 10))  # 调整+1，测试值行
                            test_list[j + 8][3] = '不合格'  # 调整索引+1，因为增加了扫码等级列
                        else:
                            self.tableWidgetRight.item(3 + i, j + 1).setBackground(
                                QtGui.QColor(10, 200, 10))  # 调整+1，测试值行
                        self.tableWidgetRight.viewport().update()
                    except VoltageError as e:
                        if not self._child_show_dialog('模拟电压读取失败, 是否继续', f'{e}'):
                            return
                try:
                    self.relay8right.closeall()
                except RelayError as e:
                    if not self._child_show_dialog('继电器关闭失败, 是否继续', f'{e}'):
                        return

        if not self.test_thread_flag:
            return

        try:
            self.presscontrol.vent()
            if left_num > 0:
                self.plccontrol.plc_assert_action('left', 0)
            if right_num > 0:
                self.plccontrol.plc_assert_action('right', 0)
        except PressureSourceError as e:
            if not self._child_show_dialog('压力卸载失败, 是否继续', f'{e}'):
                return

        # 分析日期是否为最新，否则重新生成一下两个月的时间。
        mylogger.info('---------------------保存测试数据中---------------------')
        new_list = []
        for data in test_list:
            if data[0] != '':  # regex
                # 调试：检查保存到Excel的数据结构
                mylogger.debug(
                    f"保存数据: 序列号='{data[0]}', 测试时间='{data[1]}', 扫码等级='{data[2]}', 是否合格='{data[3]}'")
                new_list.append(data)
        today_year = self.xlsxclass[1].year
        today_month = self.xlsxclass[1].month
        for data in new_list:
            if data[1] == '':
                continue
            # 获得测试时间
            test_time = time.strptime(data[1], "%Y-%m-%d %H:%M:%S")
            # 如果测试时间大于今天的时间，说明是新的数据，需要重新生成一下xlsx文件
            if today_year < test_time.tm_year or today_month < test_time.tm_mon:
                del self.xlsxclass
                self.xlsx_init()
        # self.save_test_data(new_list)

        save_again = True
        while save_again:
            flag = False
            for xlsx in self.xlsxclass:
                if xlsx.is_open():
                    flag = True
            if flag:
                save_again = self._child_show_dialog('保存数据失败，请关闭文件后重试', '是否重试')
            else:
                break

        if save_again:
            self.save_test_data(new_list)
        else:
            mylogger.info('<font color=red>放弃保存</font>')

        mylogger.info('保存数据结束!')
        self.stop_test_timer_keep_display()
        # 不合格器件的序列号标红，合格的标绿
        # 如果有不合格的，进入不合格处理流程
        # 注意这里test_list默认都是合格 (即使是空的)
        is_unqualified = [True if data[3] == '不合格' else False for data in test_list]  # 调整索引为3
        for i in range(16):
            if test_list[i][1] == '':
                continue

            # 在界面上显示是否合格状态
            status = test_list[i][3]  # 获取合格状态
            scan_grade = test_list[i][2]  # 获取扫码等级用于调试
            mylogger.debug(f"产品{i}: 扫码等级='{scan_grade}', 是否合格='{status}'")

            # 确保显示的是合格状态而不是扫码等级
            if status in ['合格', '不合格']:
                display_status = status
            else:
                # 如果状态不正确，强制设为合格
                display_status = '合格'
                mylogger.warning(f"产品{i}的合格状态异常: '{status}', 已强制设为'合格'")
            if i < 8:
                self.tableWidgetLeft.setItem(2, i + 1, QTableWidgetItem(display_status))  # 在第2行显示合格状态
            else:
                self.tableWidgetRight.setItem(2, i - 7, QTableWidgetItem(display_status))  # 在第2行显示合格状态

            # 设置背景颜色
            if is_unqualified[i]:
                if i < 8:
                    self.tableWidgetLeft.item(0, i + 1).setBackground(QtGui.QColor(200, 10, 10))
                    self.tableWidgetLeft.item(2, i + 1).setBackground(QtGui.QColor(200, 10, 10))  # 合格状态也标红
                else:
                    self.tableWidgetRight.item(0, i - 7).setBackground(QtGui.QColor(200, 10, 10))
                    self.tableWidgetRight.item(2, i - 7).setBackground(QtGui.QColor(200, 10, 10))  # 合格状态也标红
            else:
                if i < 8:
                    self.tableWidgetLeft.item(0, i + 1).setBackground(QtGui.QColor(10, 200, 10))
                    self.tableWidgetLeft.item(2, i + 1).setBackground(QtGui.QColor(10, 200, 10))  # 合格状态也标绿
                else:
                    self.tableWidgetRight.item(0, i - 7).setBackground(QtGui.QColor(10, 200, 10))
                    self.tableWidgetRight.item(2, i - 7).setBackground(QtGui.QColor(10, 200, 10))  # 合格状态也标绿
            self.tableWidgetLeft.viewport().update()
            self.tableWidgetRight.viewport().update()

        if any(is_unqualified):
            mylogger.warning('检测到不合格器件, 进入不合格处理流程...')
            self.setLed(self.ledLabel, 'yellow', 64)
            self.unqualified_process_flag = True

            # 打开扫码线程，将不合格的序列号扫入，表示取出不合格器件，此时tablewidget闪烁后变成黄色
            mylogger.warning('请扫描不合格器件...')
            while any(is_unqualified):
                self.scan_flag = True
                time.sleep(0.5)
                # 这里通过检查颜色来判断是否扫码正确
                for i in range(16):
                    if test_list[i][1] == '':
                        continue
                    if is_unqualified[i]:
                        if i < 8:
                            left_item = self.tableWidgetLeft.item(0, i + 1)
                            if left_item is not None and left_item.background().color() == QtGui.QColor(200, 200, 10):
                                is_unqualified[i] = False
                        else:
                            right_item = self.tableWidgetRight.item(0, i - 7)
                            if right_item is not None and right_item.background().color() == QtGui.QColor(200, 200, 10):
                                is_unqualified[i] = False

            self.scan_flag = False
            self.unqualified_process_flag = False
            mylogger.info('<font color=green>不合格器件处理完成</font>')

        # 更新一下更新的位置。
        self.scan_place = 'Left'
        self.scan_column = 1
        self.scan_flag = True
        # 停止计时器但保留时间显示
        ## self.stop_test_timer_keep_display()
        self.update_test_status("测试完成", "#d4edda")

        self.setLed(self.ledLabel, 'gray', 64)
        msg = '---------------------测试完成---------------------'
        mylogger.info(f'<font color=green>{msg}</font>')
        self.set_button_in_test(True)
        self.test_thread_flag = False

    # test_list如何写入xls文件部分的代码
    # 独立线程，写完就关
    @log_instance('初始化xlsx文件', mylogger)
    def xlsx_init(self):
        path = self.resDataPath
        today = datetime.date.today()

        first = today.replace(day=1)
        # 3. 减一天，得到上个月的最后一天
        last_month = first - datetime.timedelta(days=1)
        # 4. 格式化成指定形式
        print(last_month.strftime("%Y%m"))
        print(today.strftime("%Y%m"))
        self.xlsxclass = []
        self.xlsxclass.append(xlsxfile.Xlsx(path, last_month.year, last_month.month))
        self.xlsxclass.append(xlsxfile.Xlsx(path, today.year, today.month))

        self.bckClass = xlsxfile.Copytobackup(path, self.bckDataPath)

    def save_test_data(self, test_list):
        # print("save data of ", self.xlsxclass[0].month)
        # time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # time_exp = time.strptime(time_now, "%Y-%m-%d %H:%M:%S")
        # print(time_exp.tm_year, time_exp.tm_mon)
        save_flags = [False, False]
        for data in test_list:
            id_same_flag = False
            test_date = data[1]  # 测试时间在索引1

            for xlsx, i in zip(self.xlsxclass, range(2)):
                # print(self.xlsxclass)
                # print(type(xlsx))

                # 使用简单的编号查找方法，同编号直接覆盖
                get_row = xlsx.searchID(data[0])
                # print(type(get_row))

                if get_row > 0:
                    # 找到了相同编号的数据，直接覆盖
                    mylogger.info(f"产品 {data[0]} 已有测试数据，将覆盖原数据")
                    xlsx.merge_data(get_row, data)
                    xlsx.fill_color(get_row, self)
                    id_same_flag = True
                    save_flags[i] = True
                    break

            if id_same_flag is False:
                # 没有找到相同编号的数据，新增记录
                get_row = self.xlsxclass[1].append_data(data)
                mylogger.info(f"产品 {data[0]} 新增测试记录到第 {get_row} 行")
                self.xlsxclass[1].fill_color(get_row, self)
                save_flags[1] = True

        for save_flag, xlsx in zip(save_flags, self.xlsxclass):
            if save_flag:
                xlsx.save()
        self.bckClass.backupfile()

    # @log_instance('更新扫码位置', mylogger)
    def scan_place_update(self, row, column, type):
        mylogger.info(f"扫描位置 {type}, {column}")
        mylogger.debug(f"self.scan_flag {self.scan_flag}")
        # print("clicked now is ", type, row, column)
        # print("self.scan_flag", self.scan_flag)
        if row == 0:
            self.scan_place = type
            self.scan_column = column
            if self.scan_column == 0:
                self.scan_column = 1

    @log_instance('启动扫码线程', mylogger)
    def scan_thread(self):  # 扫码线程，受两个flag控制
        while self.scan_thread_flag:
            if not self.scan_flag:
                time.sleep(0.5)
                continue
            self.setLed(self.ledLabel, 'grey', 64)

            log_counter = 10
            if log_counter == 10:
                if not self.unqualified_process_flag:
                    mylogger.info('等待扫码中...')
                else:
                    mylogger.info('<font color=orange>请扫描不合格器件...</font>')
                log_counter = 0
            log_counter += 1
            ##解决这边字符串所导致str没有read_id问题
            if hasattr(self.scanid, 'read_id') and callable(getattr(self.scanid, 'read_id')):
                scan_result = self.scanid.read_id()
            else:
                scan_result = None
            # print("scan thread is running")
            # 在增加扫码等级时，增加一个返回值，然后在列表同时更新
            same_flag = False
            id_str = None
            grade_str = ''

            # 处理扫码结果
            if scan_result is not None:
                if isinstance(scan_result, tuple) and len(scan_result) == 2:
                    # 新格式：返回(序列号, 扫码等级)
                    id_str, grade_str = scan_result
                else:
                    # 兼容旧格式：只返回序列号
                    id_str = scan_result
                    grade_str = ''

            if id_str is not None:
                # 检查是否处于扫码等级模式
                if self.scan_grade_mode:
                    self.handle_scan_grade_result(id_str, grade_str)
                    continue

                if grade_str:
                    mylogger.info(f"扫码到ID: {id_str}, 等级: {grade_str}")
                else:
                    mylogger.info(f"扫码到ID: {id_str}")
                # 不合格器件处理流程
                # 将扫码到的ID跟表格中的ID进行对比，找到对应的位置
                # 如果是不合格器件，则闪烁后变成黄色
                # 如果是合格器件，则只是闪烁
                if self.unqualified_process_flag:
                    find_flag = False
                    for i in range(16):
                        if i < 8:
                            left_item = self.tableWidgetLeft.item(0, i + 1)
                            if left_item is not None and \
                                    id_str == left_item.text() and \
                                    left_item.background().color() == QtGui.QColor(200, 10, 10):
                                left_item.setBackground(QtGui.QColor(0, 0, 0))
                                self.tableWidgetLeft.viewport().update()
                                time.sleep(0.5)
                                left_item.setBackground(QtGui.QColor(200, 200, 10))
                                self.tableWidgetLeft.viewport().update()
                                find_flag = True
                                break
                        else:
                            right_item = self.tableWidgetRight.item(0, i - 7)
                            if right_item is not None and \
                                    id_str == right_item.text() and \
                                    right_item.background().color() == QtGui.QColor(200, 10, 10):
                                right_item.setBackground(QtGui.QColor(0, 0, 0))
                                self.tableWidgetRight.viewport().update()
                                time.sleep(0.5)
                                right_item.setBackground(QtGui.QColor(200, 200, 10))
                                self.tableWidgetRight.viewport().update()
                                find_flag = True
                                break
                    if not find_flag:
                        mylogger.warning(f"ID: {id_str}, 没有找到对应器件, 或者不是不合格器件")

                    # 重头开始扫码
                    continue

                for j in range(8):  # 如果序列号相同，则不更新
                    if self.tableWidgetLeft.item(0, j + 1) is not None and \
                            id_str == self.tableWidgetLeft.item(0, j + 1).text():
                        print(id_str, "is the same with Left ", self.scan_column)
                        same_flag = True
                        continue
                    if self.tableWidgetRight.item(0, j + 1) is not None and \
                            id_str == self.tableWidgetRight.item(0, j + 1).text():
                        print(id_str, "is the same with Right ", j + 1)
                        same_flag = True
                        continue
                if same_flag and not self.scan_place == 'Search':
                    mylogger.warning(f"ID: {id_str}, 与之前的ID重复")
                    continue

                if self.scan_place == 'Left':
                    # 在这里更新一下码和等级
                    # if self.tableWidgetLeft.item(0, self.scan_column) is None:
                    self.tableWidgetLeft.setItem(0, self.scan_column, QTableWidgetItem(id_str))
                    # 设置扫码等级
                    if grade_str:
                        self.tableWidgetLeft.setItem(1, self.scan_column, QTableWidgetItem(grade_str))
                    self.tableWidgetLeft.viewport().update()
                    # self.tableWidgetLeft.item(0, self.scan_column).setText(id_str)
                    if self.scan_column == 8:
                        self.scan_place = 'Right'
                        self.scan_column = 1
                    else:
                        self.scan_column = self.scan_column + 1
                elif self.scan_place == 'Right':
                    # self.tableWidgetRight.item(0, self.scan_column).setText(id_str)
                    self.tableWidgetRight.setItem(0, self.scan_column, QTableWidgetItem(id_str))
                    # 设置扫码等级
                    if grade_str:
                        self.tableWidgetRight.setItem(1, self.scan_column, QTableWidgetItem(grade_str))
                    self.tableWidgetRight.viewport().update()
                    if self.scan_column == 8:
                        self.scan_place = ''
                        self.scan_column = 1
                    else:
                        self.scan_column = self.scan_column + 1
                elif self.scan_place == 'Search':
                    self.searchTableWidget.setItem(0, 0, QTableWidgetItem(id_str))
                    self.searchTableWidget.viewport().update()
                    # 移除自动查询，只填入扫码结果，需要手动点击查询按钮
                    mylogger.info(f"扫码完成: {id_str}，请点击查询按钮进行查询")
            # time.sleep(0.2)

    def setLed(self, led, color, size):
        led.setText("")
        min_width = "min-width: " + str(size) + ";"
        min_height = "min-height: " + str(size) + ";"
        max_width = "max-width: " + str(size) + ";"
        max_height = "max-height: " + str(size) + ";"
        border_radius = "border-radius: " + str(size / 2) + ";"
        border = "border: 1px solid black;"
        background_color = "background-color: " + str(color) + ";"
        led.setStyleSheet(min_width + min_height + max_width + max_height + border_radius + border + background_color)

    def init_test_status_display(self):
        """初始化测试状态显示"""
        try:
            # 检查UI组件是否存在，如果不存在则创建
            if not hasattr(self, 'testStatusLabel'):
                # 如果UI文件没有这些组件，我们需要手动创建
                ##self.create_status_widgets()
                pass

            # 初始化状态显示
            self.update_test_status("待机中", "#f0f0f0")
            self.update_test_time("00:00:00")

        except Exception as e:
            mylogger.warning(f"初始化测试状态显示失败: {e}")

    def create_status_widgets(self):
        """手动创建状态显示组件（如果UI文件中没有）"""
        try:
            # 创建状态标签
            self.testStatusLabel = QLabel(self)
            self.testStatusLabel.setGeometry(1160, 305, 161, 30)
            self.testStatusLabel.setText("状态：待机中")
            self.testStatusLabel.setAlignment(QtCore.Qt.AlignCenter)
            self.testStatusLabel.setStyleSheet("""
                QLabel {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 5px;
                    padding: 5px;
                    font-weight: bold;
                }
            """)
            # 确保组件可见
            self.testStatusLabel.show()
            self.testStatusLabel.raise_()

            # 创建时间标签
            self.testTimeLabel = QLabel(self)
            self.testTimeLabel.setGeometry(1160, 345, 161, 30)
            self.testTimeLabel.setText("用时：00:00:00")
            self.testTimeLabel.setAlignment(QtCore.Qt.AlignCenter)
            self.testTimeLabel.setStyleSheet("""
                QLabel {
                    background-color: #e8f4fd;
                    border: 1px solid #4a90e2;
                    border-radius: 5px;
                    padding: 5px;
                    font-family: 'Courier New', monospace;
                    font-size: 12px;
                    font-weight: bold;
                }
            """)
            # 确保组件可见
            self.testTimeLabel.show()
            self.testTimeLabel.raise_()

            mylogger.info("手动创建状态显示组件成功")

        except Exception as e:
            mylogger.error(f"创建状态显示组件失败: {e}")

    def init_test_timer(self):
        """初始化测试计时器"""
        # 计时器相关变量
        self.test_start_time = None
        self.test_elapsed_seconds = 0
        self.test_is_running = False

        # 创建QTimer
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.update_timer_display)
        self.test_timer.setInterval(1000)  # 每秒更新一次

        mylogger.info("测试计时器初始化完成")

    def start_test_timer(self):
        """开始测试计时"""
        self.test_start_time = datetime.datetime.now()
        self.test_elapsed_seconds = 0
        self.test_is_running = True
        self.test_timer.start()
        mylogger.info("测试计时器已启动")

    def stop_test_timer(self):
        """停止测试计时"""
        self.test_timer.stop()
        self.test_is_running = False
        mylogger.info(f"测试计时器已停止，总用时: {self.format_time(self.test_elapsed_seconds)}")

    def stop_test_timer_keep_display(self):
        """停止测试计时但保留时间显示"""
        self.test_timer.stop()
        self.test_is_running = False
        # 保留最后的时间显示，不清空
        mylogger.info(f"测试计时器已停止，保留显示时间: {self.format_time(self.test_elapsed_seconds)}")

    def reset_test_timer(self):
        """重置测试计时器"""
        self.test_timer.stop()
        self.test_start_time = None
        self.test_elapsed_seconds = 0
        self.test_is_running = False
        self.update_test_time("00:00:00")
        mylogger.info("测试计时器已重置")

    def update_timer_display(self):
        """更新计时器显示"""
        if self.test_is_running and self.test_start_time:
            current_time = datetime.datetime.now()
            elapsed = current_time - self.test_start_time
            self.test_elapsed_seconds = int(elapsed.total_seconds())
            time_str = self.format_time(self.test_elapsed_seconds)
            # 添加调试日志
            mylogger.debug(f"计时器更新: {time_str}")
            self.update_test_time(time_str)

    def format_time(self, seconds):
        """格式化时间显示"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"

    def update_test_status(self, status, color="#f0f0f0"):
        """更新测试状态显示"""
        try:
            if hasattr(self, 'testStatusLabel'):
                self.testStatusLabel.setText(f"状态：{status}")
                # 更新背景颜色
                '''
                current_style = self.testStatusLabel.styleSheet()
                new_style = current_style.replace(
                    "background-color: #f0f0f0",
                    f"background-color: {color}"
                ).replace(
                    "background-color: #d4edda",
                    f"background-color: {color}"
                ).replace(
                    "background-color: #f8d7da",
                    f"background-color: {color}"
                ).replace(
                    "background-color: #fff3cd",
                    f"background-color: {color}"
                )
                self.testStatusLabel.setStyleSheet(new_style)
                '''
        except Exception as e:
            mylogger.warning(f"更新测试状态失败: {e}")

    def update_test_time(self, time_str):
        """更新测试时间显示"""
        try:
            if hasattr(self, 'testTimeLabel'):
                self.testTimeLabel.setText(f"用时：{time_str}")
                # 强制UI更新
                self.testTimeLabel.update()
                self.testTimeLabel.repaint()
                # 添加调试日志
                mylogger.debug(f"时间显示已更新: {time_str}")
        except Exception as e:
            mylogger.warning(f"更新测试时间失败: {e}")


