import time

import serialsetting
import re
from log_exception import VoltageError
import logging

volLogger = logging.getLogger("vol")
rotatingFileHandler = logging.handlers.RotatingFileHandler("logs/vol.log", maxBytes=1024 * 1024, backupCount=100, encoding='utf-8')
# [%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s, encoding='utf-8'
formatter = logging.Formatter("[%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s")
rotatingFileHandler.setFormatter(formatter)
volLogger.addHandler(rotatingFileHandler)
volLogger.setLevel(logging.DEBUG)
class VolTest:

    def __init__(self, port, baudrate=9600):
        self.serial = serialsetting.ComSerial(port, baudrate=baudrate, timeout=1)


    def read_vol(self):
        try:
            read_str = b':DATA?\n'
            vol_str = self.serial.write_and_read_line(read_str, 0.1)
            pattern = re.compile(r'(?<=:DATA )\d+\.?\d*')
            # print(type(vol_str))
            print(vol_str)
            vol = float(pattern.findall(vol_str.decode())[0])
            return vol
        except Exception as e:
            raise VoltageError(e)

    def read_vol_avg(self):
        # vol_all = 0
        # for i in range(3):
        #     vol_all += self.read_vol()
        #     time.sleep(0.01)
        # return vol_all/3
        try:
            read_str = b':DATA?\n'
            vol_strs = self.serial.write_and_read_line_times(read_str, 0.1, 3)
            pattern = re.compile(r'(?<=:DATA )\d+\.?\d*')
            # print(type(vol_str))
            print(vol_strs)
            volLogger.debug(f'电压值为{vol_strs}')
            vol = 0
            for vol_str in vol_strs:
                vol += float(pattern.findall(vol_str.decode())[0])
            return vol/3
        except Exception as e:
            raise VoltageError(e)

if __name__ == '__main__':
    vol = VolTest("COM8")
    for i in range(5):
        print(vol.read_vol())