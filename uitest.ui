<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1380</width>
    <height>830</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <widget class="QScrollArea" name="scrollArea">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>0</y>
      <width>1361</width>
      <height>771</height>
     </rect>
    </property>
    <property name="widgetResizable">
     <bool>true</bool>
    </property>
    <property name="alignment">
     <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
    </property>
    <widget class="QWidget" name="scrollAreaWidgetContents">
     <property name="geometry">
      <rect>
       <x>0</x>
       <y>0</y>
       <width>1359</width>
       <height>769</height>
      </rect>
     </property>
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>1359</width>
       <height>769</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QTabWidget" name="tabWidget">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>华光细黑_CNKI</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="currentIndex">
         <number>2</number>
        </property>
        <widget class="QWidget" name="tab">
         <attribute name="title">
          <string>设置</string>
         </attribute>
         <widget class="QComboBox" name="SerialPressure">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>60</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QPushButton" name="pushButton">
          <property name="geometry">
           <rect>
            <x>110</x>
            <y>550</y>
            <width>171</width>
            <height>41</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>刷新测试串口号</string>
          </property>
         </widget>
         <widget class="QLabel" name="label">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>40</y>
            <width>111</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>压力源串口号</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_2">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>90</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>SENT接口串口号</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_3">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>140</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>电压表串口号</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_4">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>190</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>左继电器串口号</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_5">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>240</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>右继电器串口号</string>
          </property>
         </widget>
         <widget class="QComboBox" name="SerialSENT">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>110</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QComboBox" name="SerialVol">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>160</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QComboBox" name="Serial8left">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>210</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QComboBox" name="Serial8right">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>260</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QLabel" name="label_7">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>450</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>左烘箱温度</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="TempLeft">
          <property name="geometry">
           <rect>
            <x>530</x>
            <y>450</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string> °C</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="minimum">
           <double>-999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1000.000000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_8">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>290</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>测量模式串口号</string>
          </property>
         </widget>
         <widget class="QComboBox" name="Serial2">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>310</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QLabel" name="label_19">
          <property name="geometry">
           <rect>
            <x>1110</x>
            <y>20</y>
            <width>111</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>采集模式</string>
          </property>
         </widget>
         <widget class="QComboBox" name="Model">
          <property name="geometry">
           <rect>
            <x>1190</x>
            <y>20</y>
            <width>131</width>
            <height>22</height>
           </rect>
          </property>
          <item>
           <property name="text">
            <string>模拟电压</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>SENT信号</string>
           </property>
          </item>
         </widget>
         <widget class="QLabel" name="label_21">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>340</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>左温箱串口号</string>
          </property>
         </widget>
         <widget class="QComboBox" name="SerialTempLeft">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>360</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QLabel" name="label_22">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>440</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>扫码枪串口号</string>
          </property>
         </widget>
         <widget class="QComboBox" name="SerialScan">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>460</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QSpinBox" name="spinBox_3">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>60</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="focusPolicy">
           <enum>Qt::WheelFocus</enum>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_4">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>110</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>115200</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_5">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>160</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_6">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>210</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_7">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>260</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_8">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>310</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_9">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>360</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QSpinBox" name="spinBox_10">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>410</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_3">
          <property name="geometry">
           <rect>
            <x>550</x>
            <y>140</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>5</number>
          </property>
          <property name="minimum">
           <double>-999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999.000000000000000</double>
          </property>
          <property name="value">
           <double>0.690500000000000</double>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_4">
          <property name="geometry">
           <rect>
            <x>840</x>
            <y>140</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>5</number>
          </property>
          <property name="minimum">
           <double>-999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>999.990000000000009</double>
          </property>
          <property name="value">
           <double>0.110500000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_23">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>140</y>
            <width>141</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>压力转换公式截距</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_24">
          <property name="geometry">
           <rect>
            <x>690</x>
            <y>140</y>
            <width>141</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>压力转换公式斜率</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_5">
          <property name="geometry">
           <rect>
            <x>530</x>
            <y>490</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="prefix">
           <string/>
          </property>
          <property name="suffix">
           <string> %</string>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>0.000000000000000</double>
          </property>
          <property name="singleStep">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>-1.500000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_25">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>490</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>低温负误差要求</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_6">
          <property name="geometry">
           <rect>
            <x>530</x>
            <y>530</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="prefix">
           <string/>
          </property>
          <property name="suffix">
           <string> %</string>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>0.000000000000000</double>
          </property>
          <property name="singleStep">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>-1.000000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_26">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>530</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>中温负误差要求</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_7">
          <property name="geometry">
           <rect>
            <x>530</x>
            <y>570</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="prefix">
           <string/>
          </property>
          <property name="suffix">
           <string> %</string>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>0.000000000000000</double>
          </property>
          <property name="singleStep">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>-1.500000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_27">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>570</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>高温负误差要求</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_2">
          <property name="geometry">
           <rect>
            <x>980</x>
            <y>470</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="prefix">
           <string>±</string>
          </property>
          <property name="suffix">
           <string> °C</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="value">
           <double>1.000000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_12">
          <property name="geometry">
           <rect>
            <x>870</x>
            <y>470</y>
            <width>101</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>温度允许波动范围</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_8">
          <property name="geometry">
           <rect>
            <x>980</x>
            <y>520</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string> °C</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>200.000000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_28">
          <property name="geometry">
           <rect>
            <x>870</x>
            <y>520</y>
            <width>111</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>低温中温界点</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_29">
          <property name="geometry">
           <rect>
            <x>870</x>
            <y>570</y>
            <width>111</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>中温高温界点</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_9">
          <property name="geometry">
           <rect>
            <x>980</x>
            <y>570</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string notr="true" comment="°C" extracomment="°C"> °C</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1000.000000000000000</double>
          </property>
          <property name="value">
           <double>80.000000000000000</double>
          </property>
         </widget>
         <widget class="QComboBox" name="Model_2">
          <property name="geometry">
           <rect>
            <x>520</x>
            <y>240</y>
            <width>71</width>
            <height>22</height>
           </rect>
          </property>
          <item>
           <property name="text">
            <string>否</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>是</string>
           </property>
          </item>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_10">
          <property name="geometry">
           <rect>
            <x>500</x>
            <y>280</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="focusPolicy">
           <enum>Qt::ClickFocus</enum>
          </property>
          <property name="suffix">
           <string> kPa</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1000.000000000000000</double>
          </property>
          <property name="value">
           <double>70.000000000000000</double>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_11">
          <property name="geometry">
           <rect>
            <x>500</x>
            <y>320</y>
            <width>101</width>
            <height>22</height>
           </rect>
          </property>
          <property name="suffix">
           <string> kPa</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="minimum">
           <double>-100.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1000.000000000000000</double>
          </property>
          <property name="value">
           <double>-6.000000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_32">
          <property name="geometry">
           <rect>
            <x>610</x>
            <y>280</y>
            <width>121</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>上钳位输出下限</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_12">
          <property name="geometry">
           <rect>
            <x>740</x>
            <y>280</y>
            <width>81</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>3</number>
          </property>
          <property name="value">
           <double>4.577000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_33">
          <property name="geometry">
           <rect>
            <x>840</x>
            <y>280</y>
            <width>131</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>上钳位输出上限</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_13">
          <property name="geometry">
           <rect>
            <x>970</x>
            <y>280</y>
            <width>81</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>3</number>
          </property>
          <property name="value">
           <double>4.673000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_34">
          <property name="geometry">
           <rect>
            <x>610</x>
            <y>320</y>
            <width>121</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>下钳位输出下限</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_35">
          <property name="geometry">
           <rect>
            <x>840</x>
            <y>320</y>
            <width>131</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>下钳位输出上限</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_14">
          <property name="geometry">
           <rect>
            <x>970</x>
            <y>320</y>
            <width>81</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>3</number>
          </property>
          <property name="value">
           <double>0.423000000000000</double>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_15">
          <property name="geometry">
           <rect>
            <x>740</x>
            <y>320</y>
            <width>81</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>3</number>
          </property>
          <property name="value">
           <double>0.327000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_36">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>390</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>右温箱串口号</string>
          </property>
         </widget>
         <widget class="QComboBox" name="SerialTempRight">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>410</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QSpinBox" name="spinBox_11">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>460</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>115200</number>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_31">
          <property name="geometry">
           <rect>
            <x>760</x>
            <y>490</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string> %</string>
          </property>
          <property name="singleStep">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>1.500000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_61">
          <property name="geometry">
           <rect>
            <x>630</x>
            <y>530</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>中温正误差要求</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_62">
          <property name="geometry">
           <rect>
            <x>630</x>
            <y>490</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>低温正误差要求</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_63">
          <property name="geometry">
           <rect>
            <x>630</x>
            <y>570</y>
            <width>121</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>高温正误差要求</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_32">
          <property name="geometry">
           <rect>
            <x>760</x>
            <y>530</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string> %</string>
          </property>
          <property name="singleStep">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>1.000000000000000</double>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_33">
          <property name="geometry">
           <rect>
            <x>760</x>
            <y>570</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string> %</string>
          </property>
          <property name="singleStep">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>1.500000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_64">
          <property name="geometry">
           <rect>
            <x>630</x>
            <y>450</y>
            <width>101</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>右烘箱温度</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="TempRight">
          <property name="geometry">
           <rect>
            <x>760</x>
            <y>450</y>
            <width>91</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="suffix">
           <string> °C</string>
          </property>
          <property name="decimals">
           <number>1</number>
          </property>
          <property name="minimum">
           <double>-999.000000000000000</double>
          </property>
          <property name="maximum">
           <double>1000.000000000000000</double>
          </property>
         </widget>
         <widget class="QLabel" name="label_37">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>490</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>PLC串口号</string>
          </property>
         </widget>
         <widget class="QComboBox" name="SerialPLC">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>510</y>
            <width>241</width>
            <height>22</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <item>
           <property name="text">
            <string>COM1</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM2</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM3</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM4</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM5</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>COM6</string>
           </property>
          </item>
         </widget>
         <widget class="QSpinBox" name="spinBox_12">
          <property name="geometry">
           <rect>
            <x>270</x>
            <y>510</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="maximum">
           <number>999999999</number>
          </property>
          <property name="value">
           <number>9600</number>
          </property>
         </widget>
         <widget class="Line" name="line">
          <property name="geometry">
           <rect>
            <x>370</x>
            <y>10</y>
            <width>20</width>
            <height>581</height>
           </rect>
          </property>
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
         </widget>
         <widget class="Line" name="line_2">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>190</y>
            <width>681</width>
            <height>20</height>
           </rect>
          </property>
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
         <widget class="Line" name="line_3">
          <property name="geometry">
           <rect>
            <x>410</x>
            <y>390</y>
            <width>671</width>
            <height>20</height>
           </rect>
          </property>
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
         <widget class="Line" name="line_4">
          <property name="geometry">
           <rect>
            <x>1090</x>
            <y>10</y>
            <width>20</width>
            <height>581</height>
           </rect>
          </property>
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
         </widget>
         <widget class="QLabel" name="label_10">
          <property name="geometry">
           <rect>
            <x>550</x>
            <y>410</y>
            <width>91</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>误差对比值</string>
          </property>
         </widget>
         <widget class="QDoubleSpinBox" name="doubleSpinBox_16">
          <property name="geometry">
           <rect>
            <x>660</x>
            <y>410</y>
            <width>81</width>
            <height>22</height>
           </rect>
          </property>
          <property name="decimals">
           <number>3</number>
          </property>
          <property name="value">
           <double>0.423000000000000</double>
          </property>
         </widget>
         <widget class="Line" name="line_5">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>90</y>
            <width>681</width>
            <height>20</height>
           </rect>
          </property>
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
         <widget class="QPushButton" name="savedPathButton">
          <property name="geometry">
           <rect>
            <x>890</x>
            <y>10</y>
            <width>161</width>
            <height>31</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>测试结果保存路径</string>
          </property>
         </widget>
         <widget class="QLineEdit" name="savedPath">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>20</y>
            <width>481</width>
            <height>20</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="acceptDrops">
           <bool>true</bool>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>C:/NCGK/today</string>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QLabel" name="label_38">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>240</y>
            <width>121</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>是否钳位测试</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_39">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>280</y>
            <width>91</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>上钳位压力</string>
          </property>
         </widget>
         <widget class="QLabel" name="label_40">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>320</y>
            <width>91</width>
            <height>31</height>
           </rect>
          </property>
          <property name="text">
           <string>下钳位压力</string>
          </property>
         </widget>
         <widget class="QLineEdit" name="backupPath">
          <property name="geometry">
           <rect>
            <x>400</x>
            <y>60</y>
            <width>481</width>
            <height>20</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="acceptDrops">
           <bool>true</bool>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>C:/NCGK/today</string>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QPushButton" name="backupPathButton">
          <property name="geometry">
           <rect>
            <x>890</x>
            <y>50</y>
            <width>161</width>
            <height>31</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>备份数据保存路径</string>
          </property>
         </widget>
         <widget class="QTabWidget" name="tabWidget_3">
          <property name="geometry">
           <rect>
            <x>1100</x>
            <y>50</y>
            <width>231</width>
            <height>551</height>
           </rect>
          </property>
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="tab_6">
           <attribute name="title">
            <string>压力写值模式</string>
           </attribute>
           <widget class="QLabel" name="label_6">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>0</y>
              <width>81</width>
              <height>21</height>
             </rect>
            </property>
            <property name="text">
             <string>点数</string>
            </property>
           </widget>
           <widget class="QTableWidget" name="tableWidget_2">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>30</y>
              <width>211</width>
              <height>481</height>
             </rect>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Sunken</enum>
            </property>
            <property name="alternatingRowColors">
             <bool>true</bool>
            </property>
            <property name="selectionMode">
             <enum>QAbstractItemView::NoSelection</enum>
            </property>
            <property name="gridStyle">
             <enum>Qt::DotLine</enum>
            </property>
            <attribute name="horizontalHeaderCascadingSectionResizes">
             <bool>false</bool>
            </attribute>
            <attribute name="horizontalHeaderHighlightSections">
             <bool>true</bool>
            </attribute>
            <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
             <bool>false</bool>
            </attribute>
            <attribute name="horizontalHeaderStretchLastSection">
             <bool>true</bool>
            </attribute>
            <attribute name="verticalHeaderCascadingSectionResizes">
             <bool>false</bool>
            </attribute>
            <attribute name="verticalHeaderShowSortIndicator" stdset="0">
             <bool>false</bool>
            </attribute>
            <attribute name="verticalHeaderStretchLastSection">
             <bool>false</bool>
            </attribute>
            <row>
             <property name="text">
              <string>测试点1</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点2</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点3</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点4</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点5</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点6</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点7</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点8</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点9</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试点10</string>
             </property>
            </row>
            <column>
             <property name="text">
              <string>压力值(kPa)</string>
             </property>
            </column>
            <item row="0" column="0">
             <property name="text">
              <string>10</string>
             </property>
            </item>
            <item row="1" column="0">
             <property name="text">
              <string>20</string>
             </property>
            </item>
            <item row="2" column="0">
             <property name="text">
              <string>30</string>
             </property>
            </item>
            <item row="3" column="0">
             <property name="text">
              <string>40</string>
             </property>
            </item>
            <item row="4" column="0">
             <property name="text">
              <string>50</string>
             </property>
            </item>
           </widget>
           <widget class="QSpinBox" name="TestPoint">
            <property name="geometry">
             <rect>
              <x>110</x>
              <y>0</y>
              <width>71</width>
              <height>22</height>
             </rect>
            </property>
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="value">
             <number>5</number>
            </property>
           </widget>
          </widget>
          <widget class="QWidget" name="tab_7">
           <attribute name="title">
            <string>压力间隔模式</string>
           </attribute>
           <widget class="QDoubleSpinBox" name="Low_Pressure_Value">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>30</y>
              <width>101</width>
              <height>21</height>
             </rect>
            </property>
            <property name="minimum">
             <double>-9999.000000000000000</double>
            </property>
            <property name="maximum">
             <double>9999.000000000000000</double>
            </property>
            <property name="singleStep">
             <double>1.000000000000000</double>
            </property>
           </widget>
           <widget class="QDoubleSpinBox" name="High_Pressure_Value">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>70</y>
              <width>101</width>
              <height>21</height>
             </rect>
            </property>
            <property name="minimum">
             <double>-9999.000000000000000</double>
            </property>
            <property name="maximum">
             <double>9999.000000000000000</double>
            </property>
           </widget>
           <widget class="QDoubleSpinBox" name="Interval_Pressure">
            <property name="geometry">
             <rect>
              <x>90</x>
              <y>110</y>
              <width>101</width>
              <height>21</height>
             </rect>
            </property>
            <property name="minimum">
             <double>-9999.000000000000000</double>
            </property>
            <property name="maximum">
             <double>9999.000000000000000</double>
            </property>
            <property name="value">
             <double>0.000000000000000</double>
            </property>
           </widget>
           <widget class="QLabel" name="label_11">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>30</y>
              <width>71</width>
              <height>16</height>
             </rect>
            </property>
            <property name="text">
             <string>起始压力值</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_13">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>70</y>
              <width>71</width>
              <height>16</height>
             </rect>
            </property>
            <property name="text">
             <string>终点压力值</string>
            </property>
           </widget>
           <widget class="QLabel" name="label_14">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>110</y>
              <width>71</width>
              <height>16</height>
             </rect>
            </property>
            <property name="text">
             <string>间隔压力值</string>
            </property>
           </widget>
          </widget>
         </widget>
         <zorder>tabWidget_3</zorder>
         <zorder>SerialPressure</zorder>
         <zorder>pushButton</zorder>
         <zorder>label</zorder>
         <zorder>label_2</zorder>
         <zorder>label_3</zorder>
         <zorder>label_4</zorder>
         <zorder>label_5</zorder>
         <zorder>SerialSENT</zorder>
         <zorder>SerialVol</zorder>
         <zorder>Serial8left</zorder>
         <zorder>Serial8right</zorder>
         <zorder>label_7</zorder>
         <zorder>TempLeft</zorder>
         <zorder>label_8</zorder>
         <zorder>Serial2</zorder>
         <zorder>label_19</zorder>
         <zorder>Model</zorder>
         <zorder>label_21</zorder>
         <zorder>SerialTempLeft</zorder>
         <zorder>label_22</zorder>
         <zorder>SerialScan</zorder>
         <zorder>spinBox_3</zorder>
         <zorder>spinBox_4</zorder>
         <zorder>spinBox_5</zorder>
         <zorder>spinBox_6</zorder>
         <zorder>spinBox_7</zorder>
         <zorder>spinBox_8</zorder>
         <zorder>spinBox_9</zorder>
         <zorder>spinBox_10</zorder>
         <zorder>doubleSpinBox_3</zorder>
         <zorder>doubleSpinBox_4</zorder>
         <zorder>label_23</zorder>
         <zorder>label_24</zorder>
         <zorder>doubleSpinBox_5</zorder>
         <zorder>label_25</zorder>
         <zorder>doubleSpinBox_6</zorder>
         <zorder>label_26</zorder>
         <zorder>doubleSpinBox_7</zorder>
         <zorder>label_27</zorder>
         <zorder>doubleSpinBox_2</zorder>
         <zorder>label_12</zorder>
         <zorder>doubleSpinBox_8</zorder>
         <zorder>label_28</zorder>
         <zorder>label_29</zorder>
         <zorder>doubleSpinBox_9</zorder>
         <zorder>Model_2</zorder>
         <zorder>doubleSpinBox_10</zorder>
         <zorder>doubleSpinBox_11</zorder>
         <zorder>label_32</zorder>
         <zorder>doubleSpinBox_12</zorder>
         <zorder>label_33</zorder>
         <zorder>doubleSpinBox_13</zorder>
         <zorder>label_34</zorder>
         <zorder>label_35</zorder>
         <zorder>doubleSpinBox_14</zorder>
         <zorder>doubleSpinBox_15</zorder>
         <zorder>label_36</zorder>
         <zorder>SerialTempRight</zorder>
         <zorder>spinBox_11</zorder>
         <zorder>doubleSpinBox_31</zorder>
         <zorder>label_61</zorder>
         <zorder>label_62</zorder>
         <zorder>label_63</zorder>
         <zorder>doubleSpinBox_32</zorder>
         <zorder>doubleSpinBox_33</zorder>
         <zorder>label_64</zorder>
         <zorder>TempRight</zorder>
         <zorder>label_37</zorder>
         <zorder>SerialPLC</zorder>
         <zorder>spinBox_12</zorder>
         <zorder>line</zorder>
         <zorder>line_2</zorder>
         <zorder>line_3</zorder>
         <zorder>line_4</zorder>
         <zorder>label_10</zorder>
         <zorder>doubleSpinBox_16</zorder>
         <zorder>line_5</zorder>
         <zorder>savedPathButton</zorder>
         <zorder>savedPath</zorder>
         <zorder>label_38</zorder>
         <zorder>label_39</zorder>
         <zorder>label_40</zorder>
         <zorder>backupPath</zorder>
         <zorder>backupPathButton</zorder>
        </widget>
        <widget class="QWidget" name="tab_2">
         <attribute name="title">
          <string>测试</string>
         </attribute>
         <widget class="QPushButton" name="pushButton_2">
          <property name="geometry">
           <rect>
            <x>1170</x>
            <y>90</y>
            <width>141</width>
            <height>51</height>
           </rect>
          </property>
          <property name="text">
           <string>开始测试</string>
          </property>
         </widget>
         <widget class="QPushButton" name="pushButton_5">
          <property name="geometry">
           <rect>
            <x>1170</x>
            <y>200</y>
            <width>141</width>
            <height>51</height>
           </rect>
          </property>
          <property name="text">
           <string>清空数据</string>
          </property>
         </widget>
         <widget class="QGroupBox" name="testStatusGroup">
          <property name="geometry">
           <rect>
            <x>1150</x>
            <y>280</y>
            <width>181</width>
            <height>160</height>
           </rect>
          </property>
          <property name="title">
           <string>测试状态</string>
          </property>
          <widget class="QLabel" name="testStatusLabel">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>25</y>
             <width>161</width>
             <height>30</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string>QLabel {
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 5px;
  font-weight: bold;
}</string>
           </property>
           <property name="text">
            <string>状态：待机中</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QLabel" name="testTimeLabel">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>65</y>
             <width>161</width>
             <height>30</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string>QLabel {
  background-color: #e8f4fd;
  border: 1px solid #4a90e2;
  border-radius: 5px;
  padding: 5px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: bold;
}</string>
           </property>
           <property name="text">
            <string>用时：00:00:00</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
          </widget>
          <widget class="QPushButton" name="terminateButton">
           <property name="geometry">
            <rect>
             <x>10</x>
             <y>105</y>
             <width>161</width>
             <height>40</height>
            </rect>
           </property>
           <property name="styleSheet">
            <string>QPushButton {
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 5px;
  font-weight: bold;
}
QPushButton:hover {
  background-color: #c82333;
}
QPushButton:pressed {
  background-color: #bd2130;
}</string>
           </property>
           <property name="text">
            <string>终止并复位</string>
           </property>
          </widget>
         </widget>
         <widget class="QTabWidget" name="tabWidget_2">
          <property name="geometry">
           <rect>
            <x>30</x>
            <y>0</y>
            <width>1101</width>
            <height>601</height>
           </rect>
          </property>
          <property name="currentIndex">
           <number>1</number>
          </property>
          <widget class="QWidget" name="tab_4">
           <attribute name="title">
            <string>左侧温箱</string>
           </attribute>
           <widget class="QTableWidget" name="tableWidgetLeft">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>10</y>
              <width>1071</width>
              <height>551</height>
             </rect>
            </property>
            <property name="lineWidth">
             <number>2</number>
            </property>
            <attribute name="horizontalHeaderCascadingSectionResizes">
             <bool>true</bool>
            </attribute>
            <attribute name="horizontalHeaderDefaultSectionSize">
             <number>100</number>
            </attribute>
            <attribute name="verticalHeaderMinimumSectionSize">
             <number>18</number>
            </attribute>
            <attribute name="verticalHeaderDefaultSectionSize">
             <number>40</number>
            </attribute>
            <row>
             <property name="text">
              <string>序列号</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>扫码等级</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>是否合格</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>上钳位</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>下钳位</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值1</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差1</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值2</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差2</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值3</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差3</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值4</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差4</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值5</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差5</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值6</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差6</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值7</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差7</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值8</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差8</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值9</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差9</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值10</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差10</string>
             </property>
            </row>
            <column>
             <property name="text">
              <string>待测参数</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件1</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件2</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件3</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件4</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件5</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件6</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件7</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件8</string>
             </property>
            </column>
            <item row="0" column="1">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="0" column="2">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="0" column="3">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="5" column="1">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="5" column="2">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="5" column="3">
             <property name="text">
              <string/>
             </property>
            </item>
           </widget>
          </widget>
          <widget class="QWidget" name="tab_5">
           <attribute name="title">
            <string>右侧温箱</string>
           </attribute>
           <widget class="QTableWidget" name="tableWidgetRight">
            <property name="geometry">
             <rect>
              <x>10</x>
              <y>10</y>
              <width>1071</width>
              <height>551</height>
             </rect>
            </property>
            <property name="lineWidth">
             <number>2</number>
            </property>
            <attribute name="horizontalHeaderCascadingSectionResizes">
             <bool>true</bool>
            </attribute>
            <attribute name="horizontalHeaderMinimumSectionSize">
             <number>31</number>
            </attribute>
            <attribute name="horizontalHeaderDefaultSectionSize">
             <number>100</number>
            </attribute>
            <attribute name="verticalHeaderMinimumSectionSize">
             <number>18</number>
            </attribute>
            <attribute name="verticalHeaderDefaultSectionSize">
             <number>40</number>
            </attribute>
            <row>
             <property name="text">
              <string>序列号</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>扫码等级</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>是否合格</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>上钳位</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>下钳位</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值1</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差1</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值2</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差2</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值3</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差3</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值4</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差4</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值5</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差5</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值6</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差6</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值7</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差7</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值8</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差8</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值9</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差9</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>测试值10</string>
             </property>
            </row>
            <row>
             <property name="text">
              <string>误差10</string>
             </property>
            </row>
            <column>
             <property name="text">
              <string>待测参数</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件1</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件2</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件3</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件4</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件5</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件6</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件7</string>
             </property>
            </column>
            <column>
             <property name="text">
              <string>器件8</string>
             </property>
            </column>
            <item row="0" column="1">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="0" column="2">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="0" column="3">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="5" column="1">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="5" column="2">
             <property name="text">
              <string/>
             </property>
            </item>
            <item row="5" column="3">
             <property name="text">
              <string/>
             </property>
            </item>
           </widget>
          </widget>
         </widget>
         <widget class="QLabel" name="ledLabel">
          <property name="geometry">
           <rect>
            <x>1200</x>
            <y>470</y>
            <width>21</width>
            <height>16</height>
           </rect>
          </property>
          <property name="text">
           <string>S</string>
          </property>
         </widget>
        </widget>
        <widget class="QWidget" name="tab_3">
         <attribute name="title">
          <string>查询</string>
         </attribute>
         <widget class="QLabel" name="label_9">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>40</y>
            <width>131</width>
            <height>21</height>
           </rect>
          </property>
          <property name="text">
           <string>待查器件序列号</string>
          </property>
         </widget>
         <widget class="QTableWidget" name="tableWidget_3">
          <property name="geometry">
           <rect>
            <x>90</x>
            <y>140</y>
            <width>1001</width>
            <height>441</height>
           </rect>
          </property>
          <attribute name="horizontalHeaderDefaultSectionSize">
           <number>180</number>
          </attribute>
          <attribute name="verticalHeaderDefaultSectionSize">
           <number>29</number>
          </attribute>
          <row>
           <property name="text">
            <string>测试点1</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点2</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点3</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点4</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点5</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点6</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点7</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点8</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点9</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点10</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点11</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点12</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点13</string>
           </property>
          </row>
          <row>
           <property name="text">
            <string>测试点15</string>
           </property>
          </row>
          <column>
           <property name="text">
            <string>温度</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>设定压力</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>测试值</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>转换压力值</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>误差</string>
           </property>
          </column>
         </widget>
         <widget class="QTableWidget" name="tableWidget_5">
          <property name="geometry">
           <rect>
            <x>330</x>
            <y>30</y>
            <width>1011</width>
            <height>71</height>
           </rect>
          </property>
          <attribute name="horizontalHeaderDefaultSectionSize">
           <number>159</number>
          </attribute>
          <row>
           <property name="text">
            <string>结果</string>
           </property>
          </row>
          <column>
           <property name="text">
            <string>测试时间</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>扫码等级</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>是否合格</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>上钳位压力</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>上钳位测试值</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>下钳位压力</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>下钳位测试值</string>
           </property>
          </column>
         </widget>
         <widget class="QTableWidget" name="searchTableWidget">
          <property name="geometry">
           <rect>
            <x>140</x>
            <y>30</y>
            <width>181</width>
            <height>41</height>
           </rect>
          </property>
          <attribute name="horizontalHeaderVisible">
           <bool>false</bool>
          </attribute>
          <attribute name="horizontalHeaderDefaultSectionSize">
           <number>180</number>
          </attribute>
          <attribute name="verticalHeaderVisible">
           <bool>false</bool>
          </attribute>
          <row>
           <property name="text">
            <string>1</string>
           </property>
          </row>
          <column>
           <property name="text">
            <string>1</string>
           </property>
          </column>
         </widget>
         <widget class="QPushButton" name="searchButton">
          <property name="geometry">
           <rect>
            <x>80</x>
            <y>80</y>
            <width>171</width>
            <height>41</height>
           </rect>
          </property>
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>查询</string>
          </property>
         </widget>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="QTextBrowser" name="textBrowser">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>13411</width>
          <height>111</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1380</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="title">
     <string>文件</string>
    </property>
    <addaction name="loadConfig"/>
    <addaction name="saveConfig"/>
    <addaction name="actionbk"/>
   </widget>
   <widget class="QMenu" name="menu_2">
    <property name="title">
     <string>关于</string>
    </property>
   </widget>
   <addaction name="menu"/>
   <addaction name="menu_2"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="loadConfig">
   <property name="text">
    <string>导入配置文件</string>
   </property>
  </action>
  <action name="saveConfig">
   <property name="text">
    <string>导出配置文件</string>
   </property>
  </action>
  <action name="resPath">
   <property name="text">
    <string>测试结果保存路径</string>
   </property>
  </action>
  <action name="actionbk">
   <property name="text">
    <string>备份测试数据</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
