# 界面介绍
**信息框**
界面底部的信息框会给出程序的运行情况，方便用户判断程序和测试的状态。

**设置界面**
![[捕获3.png]]
该界面包含测试过程的一系列设置。

![[捕获4.png]]
左上角文件栏有三个选项：
- 前两个选项可以用于导入导出配置文件 (json文件)，快速更改测试过程的一系列设置。
- 第三个选项暂时没有实现，因为每次测试会自动对测试数据进行备份，备份文件存储在"设置"中的"备份数据保存路径"。

**查询界面**
![[捕获.png]]
- 切换到查询界面时，扫码焦点会自动转到查询窗口。
- 扫码后，器件编号会自动填入，并且程序会自动查询结果，也可以手动点击查询按钮。
- 被查器件如果误差超出允许值，将会标红。
- 如果结果不存在，将会弹窗报错，请检查该器件是否曾经测试过，或者是否更换过了保存路径。

**测试界面**
![[捕获2.png]]
- 测试界面中，单击对应的单元格，将会切换扫码焦点，下一次扫码将会自动把器件编号填入。
- 扫入一个器件之后，单元格焦点将会向右移动一格，因此不必对每个器件都单击一次单元格。
- 右边有"开始测试"，"清空数据"，"停止并复位"三个选项：
	1. 开始测试：点击后将会开始测试过程。
	2. 清空数据：点击后将会清空左右两个温箱的所有内容 (除了待测参数)。
	3. 停止并复位：点击后，先会等待当前操作结束 (如压力稳定过程)，强行终止测试过程并复位。
- 右边有一个LED灯，用不同颜色表示测试程序的运行状态：
	- 灰色：测试未开始。
	- 绿色：测试正常进行中。
	- 黄色：测试出现错误，但是仍可以正常进行 (比如压力稳定失败)。
	- 红色：测试出现错误，测试过程不能正常进行。

# 正常测试流程
1. 在设置界面，指定好测试过程的各种配置。
2. 切换到测试界面，**点击对应的单元格**，然后进行**扫码**，观察到器件序列号将会**自动填入**该单元格。将所有需要测试的器件扫码完成。
3. 单击开始测试，此时测试过程将会自动开始进行，测试过程中的具体信息将会显示在下方的**信息框**中。而且其他按键将会无法点击，防止影响测试。
4. 测试结束后，将会**自动保存测试数据**到设置界面填好的路径中的excel文件中。
5. 测试数据保存完成后，如果有不合格器件，将会进入**不合格器件处理流程**，此时其他按键仍然无法点击。
6. 完成不合格器件处理流程后，测试正式结束，所有其他**按键将恢复**。

# 不合格器件处理流程
不合格器件会使用红色标出。如果在测试过程中有不合格的器件，测试结束后将会观察到下方**信息框**中蓝色字体提示"**进入不合格器件处理流程**"，并且不断以黄色字体提示"**请扫码不合格器件**"
![[捕获8 1.png]]

用户需要把不合格器件在扫码器中扫描，扫描之后，对应的器件**单元格将会变成黄色**，并且信息框会提示扫描到的序列号。
![[捕获9.png]]

当所有不合格器件完成扫描，将会自动退出不合格器件处理流程，测试结束。可以观察到信息框中的**绿色提示**。
![[捕获10.png]]

# 报错
**提供选项的错误**
该类错误一般出现在测试过程中，出错后测试流程将暂停，程序将等待用户选择继续测试，或者选择终止测试。可能导致该错误出现的情况可能有：温度不能稳定、压力不能稳定、保存文件失败等等。

比如下图中弹窗提示测试数据无法保存，关闭表格文件解除文件占用之后点击"Yes"即可重新保存：![[捕获7 1.png]]

**不能继续测试的错误**
该类错误表示程序遇到了预料之外的情况，可能在整个程序的运行过程中出现，但是也大多出现在测试过程中。出现该类错误后，将会强制停止测试，并且进行复位。

比如下图中，由于无法打开所需串口，导致了测试程序无法继续进行。
![[捕获11.png]]

如果出现该类错误，请先检查硬件功能是否正常。

如果确认硬件功能正确无误，但是还是持续出错，请联系开发者解决问题，并且将程序目录下logs文件夹中的所有日志文件发送给开发者，以便开发者确定出错原因。
![[捕获16.png]]