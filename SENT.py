import serial
import serialsetting
import time
from log_exception import SENTError
import relay
import logging
import logging.config
import json
from log_exception import PressureSourceError

logging.config.dictConfig(json.load(open('logging_config.json')))
sentLogger = logging.getLogger("SENT")

"""
关于SENT串口通信的一些基本规则
1. 首byte是0x02, 第二byte表示数据长度，第三byte是命令码，之后紧跟数据，再是校验和，最后是0x03
2. 除去回应上位机的查询命令，其他的命令id都是0x64和0x65 (说明只有SENT1有数据)
3. 上位机的查询命令是两个通道都会发一遍，但是只会接到一个通道的回应
4. 观察后猜测，对于查询、写入等命令，可以使用read_and_write，因为对应的回复只有一次，可以阻塞等待
5. 后来的数据是重复发送的，但是大部分不会粘在一起 (粘一起的直接丢弃即可)，发送开始于通道配置命令

问题
1. config通道之后就会不断收到数据，而不是start
2. 发送stop通道之后也不一定会停止 (第一次stop后又接了config，并没有停止; 第二次stop后通信记录就到头了)
"""
class SENT:
    # 初始化串口
    def __init__(self, port, baudrate=115200):
        self.serial = serial.Serial(port=port, baudrate=baudrate, timeout=0.5)
        self.ser_sleep_time = 0.2
        self.sent_sleep_time = 0.5
        self.data_start = 3
        self.data_end = -2

    def _uint8(self, data):
        return data & 0xFF

    # 针对nibbles使用table计算crc (nibbles是数据帧，不包括data首尾)
    def _cal_crc(self, nibbles: list):
        numNibbles = len(nibbles)
        # crc4 table for poly 0x1d
        crc4Table = [0, 13, 7, 10, 14, 3, 9, 4, 1, 12, 6, 11, 15, 2, 8, 5]

        checkSum16 = 5
        for i in range(numNibbles):
            checkSum16 = self._uint8(nibbles[i]) ^ self._uint8(crc4Table[checkSum16])
        checkSum16 = self._uint8(0) ^ self._uint8(crc4Table[checkSum16])
        return checkSum16

    # 解读fast data
    def _extract_nibbles_from_fast(self, data: list, swapNibbles=0):
        # 获取状态位和数据长度
        status = data[3] & 0x0F
        length = data[3] >> 4
        
        if swapNibbles:
            status, length = length, status
        # 获取数据
        nibbles = []
        for i in range(4, 4+length//2):
            nib0 = data[i] & 0x0F
            nib1 = data[i] >> 4
            if swapNibbles:
                nib0, nib1 = nib1, nib0
            nibbles.append(nib0)
            nibbles.append(nib1)
        return nibbles

    # 对nibbles解码, 返回数据
    def _decode_nibbles(self, nibbles: list):
        data = 0
        for i in range(3):
            data = (data << 4) + nibbles[i]
        return data

    # 针对整个data计算crc
    def _cal_crc_from_fast(self, data: list, swapNibbles=0):
        return self._cal_crc(self._extract_nibbles_from_fast(data, swapNibbles))

    def _check_crc(self, data: list):
        crc = self._cal_crc_from_fast(data)
        return (crc == data[-3] & 0x0F) and (crc == data[-3] >> 4)

    # 计算校验和
    def _cal_sum(self, data: list):
        sum = 0
        for i in range(1, len(data)-2):
            sum += data[i]
        return sum & 0xFF

    # 检查校验和
    def _check_sum(self, data: list):
        sum = self._cal_sum(data)
        return sum == data[-2]

    # 检查并返回数据 此处是阻塞等待
    def _check_sum_and_return_data(self, frame:list):
        strs = bytes(frame)
        while True: # 重复发送直到收到正确的数据
            data = self.serial.write_and_read_line(strs, self.ser_sleep_time)
            if data[2] == frame[2] and self._check_sum(data): # 收到对应的id和sum正确
                break
            time.sleep(self.sent_sleep_time)
        return data[self.data_start:self.data_end]

    # 发送数据 不等待
    def _send_data(self, frame: list):
        strs = bytes(frame)
        self.serial.write(strs)

    # 请求：查询序列号 02 01 5A 5B 03
    def q_serial_number(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x5A
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求：读取通道1/2的配置 02 01 01/0B 02/0C 03
    def q_channel_config(self, channel):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x01 if channel == 1 else 0x0B
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 写：设置通道1/2的配置 02 08 02/0C data sum 03
    def w_channel_config(self, channel, data: list):
        str = list(range(12))
        str[0] = 0x02
        str[1] = 0x08
        str[2] = 0x02 if channel == 1 else 0x0C
        for i in range(7):
            str[3+i] = data[i]
        str[10] = self._cal_sum(str)
        str[11] = 0x03
        self._send_data(str)

    # 启动通道1/2 02 01 15/1F 16/20 03
    def start_channel(self, channel):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x15 if channel == 1 else 0x1F
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 停止通道1/2 02 01 16/20 17/21 03
    def stop_channel(self, channel):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x16 if channel == 1 else 0x20
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求：读取HW info 02 01 5B 5C 03
    def q_hw_info(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x5B
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求：读取SW info 02 01 5C 5D 03
    def q_hw_info(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x5C
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求：读取状态 02 01 5D 5E 03
    def q_status(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x5D
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)
    
    # 请求：读取AO1 config 02 01 46 47 03
    def q_ao1_config(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x46
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求：读取AO1 limits 02 01 48 49 03
    def q_ao1_limits(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x48
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求：读取AO2 config 02 01 4A 4B 03
    def q_ao2_config(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x4A
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)
    
    # 请求：读取AO2 limits 02 01 4C 4D 03
    def q_ao2_limits(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x4C
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 请求: 读取sw info
    def q_sw_info(self):
        str = list(range(5))
        str[0] = 0x02
        str[1] = 0x01
        str[2] = 0x5C
        str[3] = self._cal_sum(str)
        str[4] = 0x03
        self._send_data(str)

    # 完整的从通道配置到启动的过程, 目前只实现通道1, fast data
    # 以下完全根据软件的步骤来
    # 默认的SENT配置: CD 02 2C 01 4E 03 00
    # 按数据看, 设置之后会不断收到数据, 只需要读取即可
    def channel1_start(self):
        data = [0xCD, 0x02, 0x2C, 0x01, 0x4E, 0x03, 0x00]
        # 读取通道1和2的配置
        self.q_channel_config(1)
        self.q_channel_config(2)
        # 查询序列号
        self.q_serial_number()
        # 读取SW info
        self.q_sw_info()
        # 读取状态
        self.q_status()
        # 读取AO1 limit 和 config
        self.q_ao1_limits()
        self.q_ao1_config()
        # 读取AO2 limit 和 config
        self.q_ao2_limits()
        self.q_ao2_config()
        # 设置通道1的配置
        # self.w_channel_config(1, data)
    
    # 读取通道1的数据, 循环从串口读出一行数据, 检查crc和sum即可解码返回
    def read_channel1_data(self):
        self.serial.flush()
        time.sleep(0.5)
        data = self.serial.read_all()
        # 截取
        lft = -1
        rig = -1
        for i in range(len(data)):
            if data[i] == 2 and i + 1 < len(data):
                frame_len = data[i + 1]
                if i + frame_len + 2 < len(data):
                    lft = i
                    rig = i + frame_len + 2
        if lft != -1:
            new_data = data[lft: rig + 2]
            if self._check_crc(new_data) and self._check_sum(new_data):
                print(self._extract_nibbles_from_fast(new_data))
                print(self._decode_nibbles(self._extract_nibbles_from_fast(new_data)))
                return self._decode_nibbles(self._extract_nibbles_from_fast(new_data))

        # self.serial.flush()
        # while True:
        #     data = self.serial.read_all()
        #     if self._check_crc(data) and self._check_sum(data):
        #         break
        # print(data)
        # return self._decode_nibbles(self._extract_nibbles_from_fast(data))

if __name__ == '__main__':
    relayleft = relay.RelayControl("COM9")
    relay2 = relay.RelayControlTwo("COM5")
    relay2.testchannel(1, 1)
    relayleft.openchannel(8)

    # 工具函数测试
    sentExample = SENT("COM3")
    sentExample.channel1_start()
    time.sleep(1)
    while 1:
        sentExample.read_channel1_data()
    # print(sentExample.read_channel1_data())
    # 计算crc 5, 8
    # print(sentExample._cal_crc_from_fast([0x02, 0x06, 0x64, 0x60, 0xC1, 0x0D, 0x00, 0x55, 0xED, 0x03]))
    # print(sentExample._cal_crc_from_fast([0x02, 0x06, 0x64, 0x60, 0xB0, 0x0F, 0x00, 0x88, 0x11, 0x03]))
    # 判断crc
    # print(sentExample._check_crc([0x02, 0x06, 0x64, 0x60, 0xC1, 0x0D, 0x00, 0x55, 0xED, 0x03]))
    # print(sentExample._check_crc([0x02, 0x06, 0x64, 0x60, 0xB0, 0x0F, 0x00, 0x88, 0x11, 0x03]))
    # 判断校验和
    # print(sentExample._check_sum([0x02, 0x06, 0x64, 0x60, 0xC1, 0x0D, 0x00, 0x55, 0xED, 0x03]))
    # print(sentExample._check_sum([0x02, 0x06, 0x64, 0x60, 0xB0, 0x0F, 0x00, 0x88, 0x11, 0x03]))