from random import random

import serial
import time
import re


class IdScan:
    thread_flag = True

    def __init__(self, port, baudrate=115200):
        self.serial = serial.Serial(port=port, baudrate=baudrate, timeout=2, parity=serial.PARITY_EVEN)
        self.init_data=b"WP,230,2\r\n"
        init_length = self.serial.write(self.init_data)
        # print("scan init")
        # print("scan id com ", self.serial.name)
        # print("scan id baudrate ", self.serial.baudrate)
        # print("scan id parity ", self.serial.parity)


    def read_id(self):
        # id_str = self.serial.readline()
        data = b'LON\r\n'
        # print("send scan id ", data)
        # id_str = self.serial.write_and_read_line(read_str, 0.5)
        return_length = self.serial.write(data)
        data_length = len(data)
        if return_length != data_length:
            print(self.serial.name, " serial send error.")
        time.sleep(0.5)
        read_data = self.serial.readline()
        # print(read_data)
        if len(read_data) == 0:
            return None
        if read_data.decode()[0:2] == 'ER':
            raise Exception("扫码枪返回错误")
        pattern = re.compile(r'S\d+\.?\d*')
        pattern2 = re.compile(r'ADLMV:([^\s\r\n]+)')

        # 提取序列号
        id_matches = pattern.findall(read_data.decode())
        id_name = id_matches[0] if id_matches else None

        # 提取扫码等级
        grade_matches = pattern2.findall(read_data.decode())
        scan_grade = grade_matches[0] if grade_matches else ''

        return id_name, scan_grade
        #从数据中拆出id和扫码等级
        # return id
