from modbus_tk import modbus_rtu
import serial
import time
import struct
import logging.config
import logging
import json
from log_exception import TemperatureError

# 日志原则：报错将直接抛异常，交给上层处理；非报错将只记录debug日志信息
tempLogger = logging.getLogger("temp")
rotatingFileHandler = logging.handlers.RotatingFileHandler("logs/temp.log", maxBytes=1024 * 1024, backupCount=100, encoding='utf-8')
# [%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s, encoding='utf-8'
formatter = logging.Formatter("[%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s")
rotatingFileHandler.setFormatter(formatter)
tempLogger.addHandler(rotatingFileHandler)
tempLogger.setLevel(logging.DEBUG)

class TempModbus:

    def __init__(self, port, baudrate=9600):
        self.conn = serial.Serial(port=port, baudrate=baudrate, bytesize=8, parity='N', stopbits=1, xonxoff=0)
        self.master = modbus_rtu.RtuMaster(self.conn)
        self.master.set_timeout(1)  # 设置超时时间
        self.master.set_verbose(True)

        self.master.close()
        self.conn.close()

    def read_temp(self):
        self.conn.open()
        self.master.open()
        try:
            response = self.master.execute(0x01, 3, 0x0218, 1)
            # print('采集结果----->', response)
            signed_number = struct.unpack('h', struct.pack('H', response[0]))[0]
            # print(snumber)
            temp_now = float(signed_number / 10)
            self.master.close()
            self.conn.close()
            return temp_now
        except:
            raise TemperatureError("读取温度时，串口无数据")
        #此处的close和open不确定是不是好用。 response的单位也要注意。

    def temp_stable(self, tempvalue, temperature_error_range):
        count = 0
        t_start = time.time()
        while count < 10:
            print("temperature is", self.read_temp())
            tempLogger.debug("temperature is %s" % self.read_temp())
            if abs(self.read_temp()-tempvalue) <= temperature_error_range:
                count = count + 1
            else:
                count = 0
            time.sleep(1)
            t_end = time.time()
            if t_end - t_start  > 60:#60未稳定
                raise TemperatureError('温度值稳定超时')
                break
            #10次在预期的温度范围内。
        return True

if __name__ == '__main__':
    temp = TempModbus("COM15")
    for i in range(2):
        print((temp.read_temp()))
        # temp_now = temp.read_temp()
        # print(temp_now[0])
        time.sleep(1)