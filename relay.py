import time

import serialsetting
from log_exception import RelayError

class RelayControl:

    def __init__(self, port, baudrate=9600):
        self.serial = serialsetting.ComSerial(port, baudrate=baudrate, timeout=1)

    def openchannel(self, number):
        if number < 1 or number > 8:
            raise RelayError("relay channel set error!")
        str = list(range(8))
        str[0] = 0x33
        str[1] = 0x01
        str[2] = 0x15
        str[3] = 0x00
        str[4] = 0x00
        str[5] = 0xFF ^ (0x01 << (number - 1))
        str[6] = 0x08
        str[7] = 0x00
        for i in range(7):
            str[7] += str[i]
        str[7] &= 0x000000FF
        strs=bytes(str)
        self.serial.write(strs)

    def closeall(self):
        str = list(range(8))
        str[0] = 0x33
        str[1] = 0x01
        str[2] = 0x13
        str[3] = 0x00
        str[4] = 0x00
        str[5] = 0x00
        str[6] = 0x00
        str[7] = 0x00
        for i in range(7):
            str[7] += str[i]
        str[7] &= 0x000000FF
        strs = bytes(str)
        self.serial.write(strs)

    def readstate(self):
        str = list(range(8))
        str[0] = 0x33
        str[1] = 0x01
        str[2] = 0x17
        str[3] = 0x00
        str[4] = 0x00
        str[5] = 0x00
        str[6] = 0x00
        str[7] = 0x4B
        strs = bytes(str)
        readdata = self.serial.write_and_read_all(strs, 0.3)
        if readdata[0] != 0x22 or readdata[1] != 0x01 or readdata[2] != 0x17:
            return 0xFFFF
        data_crc = 0
        for i in range(7):
            data_crc += readdata[i]
        data_crc &= 0x000000FF
        if data_crc != readdata[7]:
            raise RelayError("relay serial crc error")
            return 0xFEFF
        return readdata[6]

    def openchannel_chek(self, number):
        self.openchannel(number)
        state = self.readstate()
        if state & 0xF000:
            raise RelayError("relay error")
        relay_state = []
        for i in range(8):
            relay_state.append(state & 0x01)
            state = state >> 1
        # raise RelayError(relay_state)
        for i in range(8):
            if i == number - 1:
                if relay_state[i] != 0:
                    raise RelayError("open realy channel {%d} error", i + 1)
            else:
                if relay_state[i] != 1:
                    raise RelayError("open realy channel {%d} error", i + 1)

class RelayControlTwo:

    def __init__(self, port, baudrate=9600):
        self.serial = serialsetting.ComSerial(port, baudrate=baudrate, timeout=1)

    def testchannel(self, mode, number): #number 1 left, number 2 right
        if number < 1 or number > 2:
            raise RelayError("relay channel set error!")
        str = list(range(8))
        str[0] = 0x33
        str[1] = 0x01
        str[2] = 0x15
        str[3] = 0x00
        str[4] = 0x00
        #str[5] = 0x03 ^ (0x01 << (number - 1))
        if mode == 0:#模拟测试
            if number == 1:
                str[5] = 0x01 #此处要详细测试修改
            else:
                str[5] = 0x02
        else:
            if number == 1: #SENT
                str[5] = 0x02
            else:
                str[5] = 0x01
        str[6] = 0x02
        str[7] = 0x00
        for i in range(7):
            str[7] += str[i]
        str[7] &= 0x000000FF
        strs=bytes(str)
        self.serial.write(strs)
    def readstate(self):
        str = list(range(8))
        str[0] = 0x33
        str[1] = 0x01
        str[2] = 0x17
        str[3] = 0x00
        str[4] = 0x00
        str[5] = 0x00
        str[6] = 0x00
        str[7] = 0x4B
        strs = bytes(str)
        readdata = self.serial.write_and_read_all(strs, 0.3)
        if readdata[0] != 0x22 or readdata[1] != 0x01 or readdata[2] != 0x17:
            return 0xFFFF
        data_crc = 0
        for i in range(7):
            data_crc += readdata[i]
        data_crc &= 0x000000FF
        if data_crc != readdata[7]:
            raise RelayError("relay serial crc error")
            return 0xFEFF
        return readdata[6]

    def testchannel_chek(self, mode, number):
        self.testchannel(mode, number)
        state = self.readstate()
        if state & 0xF000:
            raise RelayError("relay error")
        relay_state = []
        for i in range(2):
            relay_state.append(state & 0x01)
            state = state >> 1
        # print(relay_state)
        for i in range(2):
            if i == number - 1:
                if relay_state[i] != 1 - mode:
                    raise RelayError("open realy channel {%d} error", i + 1)
            else:
                if relay_state[i] != mode:
                    raise RelayError("open realy channel {%d} error", i + 1)

if __name__ == '__main__':
    # relay8 = RelayControl("COM9")
    # for i in range(8):
    #     time.sleep(2)
    #     relay8.openchannel_chek(i+1)
    #     raise RelayError(relay8.readstate())
    #     state = relay8.readstate()
    #     if state & 0xF000:
    #         raise RelayError("relay error")
    #     relay_state = []
    #     for i in range(8):
    #         relay_state.append(state & 0x01)
    #         state = state >> 1
    #     print(relay_state)
    #relay8.closeall()
    relay2 = RelayControlTwo("COM5")
    relay2.testchannel(0,2)
    raise RelayError(relay2.readstate())
    relay2.testchannel_chek(0,1)