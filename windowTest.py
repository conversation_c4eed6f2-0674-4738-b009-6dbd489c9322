import sys
from PyQt5.QtWidgets import QApplication, QPushButton, QVBoxLayout, QWidget, QMessageBox
from PyQt5.QtCore import QThread, pyqtSignal, QSemaphore

class WorkerThread(QThread):
    # 定义信号，通知主线程弹出对话框，并传递结果
    show_dialog_signal = pyqtSignal()

    def __init__(self, semaphore):
        super().__init__()
        self.semaphore = semaphore
        self.dialog_result = None  # 用于保存主线程返回的对话框结果

    def run(self):
        print("子线程：请求弹出对话框...")
        self.show_dialog_signal.emit()  # 发送信号，通知主线程弹框
        self.semaphore.acquire()  # 等待主线程释放信号量

        if self.dialog_result == QMessageBox.Yes:
            print("子线程：用户选择继续执行")
        else:
            print("子线程：用户选择停止执行")
        
        print("子线程：继续执行其他操作...")

class MainWindow(QWidget):
    def __init__(self):
        super().__init__()

        self.semaphore = QSemaphore(0)  # 创建信号量，初始值为 0
        self.worker_thread = WorkerThread(self.semaphore)  # 创建子线程

        self.initUI()

        # 连接子线程的信号到主线程的槽函数
        self.worker_thread.show_dialog_signal.connect(self.show_dialog)

    def initUI(self):
        layout = QVBoxLayout()
        self.button = QPushButton("开始测试", self)
        self.button.clicked.connect(self.start_test)

        layout.addWidget(self.button)
        self.setLayout(layout)

    def start_test(self):
        self.worker_thread.start()  # 启动子线程

    def show_dialog(self):
        # 主线程显示对话框，获取用户输入
        reply = QMessageBox.question(self, '提示', '是否继续执行？', 
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes)

        # 将结果传回子线程
        self.worker_thread.dialog_result = reply
        self.semaphore.release()  # 释放信号量，允许子线程继续执行

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())
