import serial
import time
import struct
from modbus_tk import modbus_rtu

import logger
import logging.config
import logging
import json
import os
from log_exception import PlcError

# 日志原则：报错将直接抛异常，交给上层处理；非报错将只记录debug日志信息
# 确保日志目录存在
log_folder = 'logs'
if not os.path.exists(log_folder):
    os.makedirs(log_folder)

logging.config.dictConfig(json.load(open('logging_config.json')))
plcLogger = logging.getLogger("plc")

class PLCModbus:

    def __init__(self, port, baudrate=9600):
        self.conn = serial.Serial(port=port, baudrate=baudrate, bytesize=8, parity='N', stopbits=1, xonxoff=0)
        self.master = modbus_rtu.RtuMaster(self.conn)
        self.master.set_timeout(1)  # 设置超时时间
        self.master.set_verbose(True)

        self.master.close()
        self.conn.close()

    def write_reg(self, address, value):
        self.conn.open()
        self.master.open()
        response = self.master.execute(0x01, 6, address, output_value=value)
        # print('采集结果----->', response)
        plcLogger.debug('plc write_reg response is %s', response)
        # signed_number = struct.unpack('h', struct.pack('H', response[0]))[0]
        # # print(snumber)
        # temp_now = float(signed_number / 10)
        self.master.close()
        self.conn.close()
        return response

    def read_reg(self, address):
        self.conn.open()
        self.master.open()
        response = self.master.execute(0x01, 3, address, 1)
        # print('采集结果----->', response)
        # signed_number = struct.unpack('h', struct.pack('H', response[0]))[0]
        # # print(snumber)
        # temp_now = float(signed_number / 10)
        self.master.close()
        self.conn.close()
        return response[0]

    def read_status(self, direction):
        self.conn.open()
        self.master.open()
        if direction == 'left':
            addresss = [0x001E, 0x0014, 0x000A]
        elif direction == 'right':
            addresss = [0x0046, 0x003C, 0x0032]  #低压前、高压前、气缸的顺序来定地址。
        else:
            raise PlcError("plc方向错误")
            return False
        status = []
        for address in addresss:
            response = self.master.execute(0x01, 3, address, 1)
            status.append(response[0]) #奇数代表有效，偶数代表无效
        # print("plc status is", status)
        plcLogger.debug('plc status is %s', status)
        self.master.close()
        self.conn.close()
        return status
    def plc_assert(self, direction, control_status): #control_status 0 松， 1 低压， 2 高压
        if direction == 'left':
            addresss = [0x001E, 0x0014, 0x000A]
        elif direction == 'right':
            addresss = [0x0046, 0x003C, 0x0032]  #低压前、高压前、气缸的顺序来定地址。
        else:
            raise PlcError("plc方向错误")
            return False

        status_list = self.read_status(direction)
        if control_status == 0:
            control_status_list = [0, 0, 0]
        elif control_status == 1:
            control_status_list = [1, 0, 1]
        elif control_status == 2:
            control_status_list = [1, 1, 1]
        else:
            raise PlcError("plc方向错误")
            print("control status is wrong!")
            return False
        status_list_odd = []
        for status in status_list:
            status_list_odd.append(status % 2)
        if status_list_odd == control_status_list:
            return True
        else:
            return False

    def plc_assert_action(self, direction, control_status): #control_status 0 松， 1 低压， 2 高压
        if direction == 'left':
            addresss = [0x001E, 0x0014, 0x000A]
        elif direction == 'right':
            addresss = [0x0046, 0x003C, 0x0032]  #低压前、高压前、气缸的顺序来定地址。
        else:
            raise PlcError("plc方向错误")
            print("plc direction error")
            return False

        status_list = self.read_status(direction)
        if control_status == 0:
            control_status_list = [0, 0, 0]
        elif control_status == 1:
            control_status_list = [1, 0, 1]
        elif control_status == 2:
            control_status_list = [1, 1, 1]
        else:
            raise PlcError('plc状态控制错误')
            print("control status is wrong!")
            return False
        status_list_odd = []
        for status in status_list:
            status_list_odd.append(status % 2)
        if status_list_odd == control_status_list:
            return True
        for i in reversed(range(3)):#必须倒序，先启气缸，再高压端，再低压端
            if status_list[i] % 2 == 1:
                self.write_reg(addresss[i], status_list[i] + 1)
                time.sleep(1.5)
                status_list[i] = status_list[i] + 1
        for status, control_status_i, address, in zip(status_list, control_status_list, addresss):
            if status % 2 != control_status_i:
                self.write_reg(address, status + 1)
                time.sleep(1.5)
        return True



if __name__ == '__main__':
    temp = PLCModbus("COM12")
    print(temp.plc_assert('right', 1))
    print(temp.read_status('right'))