from PyQt5.QtCore import pyqtSignal, QObject
from PyQt5.QtWidgets import QTextBrowser, QMessageBox
import logging

# # logging装饰器，用于记录函数的调用信息
# def log_name(name, logger):
#     def log_basic(func):
#         def wrapper(*args, **kwargs):
#             try:
#                 logger.debug(f"Calling function {func.__name__}")
#                 logger.info(f"{name}...")
#                 res = func(*args, **kwargs)
#                 logger.debug(f"{name} 成功")
#                 logger.debug("The result is: " + str(res))
#                 return res
#             except PermissionError as e:
#                 logger.error(f"{name} 失败, 文件被占用, 请关闭文件后重试")
#             except Exception as e:
#                 logger.error(f"{name} 失败")
#                 logger.exception(f"出现异常: {e}")
#         return wrapper
#     return log_basic

# 这种写法专门针对类的方法，因为类的方法第一个参数是self，所以wrapper要多一个参数
def log_instance(name, logger):
    def log_name(func):
        def wrapper(self, *args, **kwargs):
            try:
                logger.debug(f"Calling function {func.__name__}")
                logger.info(f"{name}...")
                res = func(self, *args, **kwargs)
                logger.debug(f"{name} 成功")
                logger.debug("The result is: " + str(res))
                return res
            except PermissionError as e:
                self.setLed(self.ledLabel, 'red', 64)
                logger.error(f"{name} 失败, 文件或串口被占用, 请解除占用后重试")
            except Exception as e:
                self.setLed(self.ledLabel, 'red', 64)
                logger.debug(f"{name} 失败, 出现异常: {e}")
                logger.exception(f"{name} 失败, 出现异常: {e}")
            finally:
                if name == '正在测试':
                    self.reset_test_config()
        return wrapper
    return log_name

class LogSignalEmitter(QObject):
    append_log = pyqtSignal(str)

class QTextBrowserHandler(logging.Handler):
    def __init__(self, text_browser: QTextBrowser):
        super().__init__()
        self.text_browser = text_browser
        self.signal_emitter = LogSignalEmitter()
        self.signal_emitter.append_log.connect(self.text_browser.append)

    def emit(self, record):
        msg = self.format(record)

        if record.levelno == logging.ERROR:
            color = 'red'
        elif record.levelno == logging.WARNING:
            color = 'blue'
        elif record.levelno == logging.INFO:
            color = 'black'
        else:
            color = 'red'

        formatted_msg = f'<font color="{color}">{msg}</font>'
        self.signal_emitter.append_log.emit(formatted_msg)


class QMessageBoxSignalEmitter(QObject):
    show_error = pyqtSignal(str)

class QMessageBoxHandler(logging.Handler):
    def __init__(self):
        super().__init__()
        self.signal_emitter = QMessageBoxSignalEmitter()
        self.signal_emitter.show_error.connect(self.show_message)

    def show_message(self, msg):
        QMessageBox.critical(None, 'Error', msg)

    def emit(self, record):
        msg = self.format(record)

        if record.levelno == logging.ERROR:
            self.signal_emitter.show_error.emit(msg)