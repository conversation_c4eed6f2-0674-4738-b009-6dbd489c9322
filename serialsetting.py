import serial
import time


class ComSerial:
    port = ""
    baudrate = 9600
    timeout = 2


    def __init__(self,port,baudrate=9600,timeout=1):
        self.port=port
        self.baudrate=baudrate
        self.timeout=timeout
        self.ser=serial.Serial(port=self.port, baudrate=self.baudrate, timeout=self.timeout)
        if  self.ser.isOpen():  # 判断串口是否成功打开
            print("打开串口成功。")
            print(self.ser.name)  # 输出串口号
        else:
            print("打开串口失败。")
        self.ser.flush()
        self.ser.close()

    def write(self, data):
        datalength=len(data)
        self.ser.open()
        returnlength=self.ser.write(data)
        if returnlength != datalength:
            print(self.ser.name,"send error")
        self.ser.close()

    def read(self, number=1):
        self.ser.open()
        readdata = self.ser.read(number)
        # if returnlength != datalength
        #    print(ser.name,"send error")
        self.ser.close()
        return readdata
    def write_and_read(self, data, number):
        self.ser.open()
        self.ser.flush()
        return_length = self.ser.write(data)
        read_data = self.ser.read(number)
        self.ser.close()
        return read_data

    def write_and_read_all(self, data, sleep_time):
        self.ser.open()
        self.ser.flush()
        return_length = self.ser.write(data)
        data_length = len(data)
        if return_length != data_length:
            print(self.ser.name, " serial send error.")
        time.sleep(sleep_time)
        read_data = self.ser.read_all()
        self.ser.close()
        return read_data

    def write_and_read_line(self, data, sleep_time):
        self.ser.open()
        self.ser.flush()
        return_length = self.ser.write(data)
        data_length = len(data)
        if return_length != data_length:
            print(self.ser.name, " serial send error.")
        time.sleep(sleep_time)
        read_data = self.ser.readline()
        self.ser.close()
        return read_data
    def write_and_read_line_times(self, data, sleep_time, times):
        self.ser.open()
        self.ser.flush()
        read_data = []
        for i in range(times):
            return_length = self.ser.write(data)
            data_length = len(data)
            if return_length != data_length:
                print(self.ser.name, "serial send error.")
            time.sleep(sleep_time)
            read_data.append(self.ser.readline())
        self.ser.close()
        return read_data

if __name__ == '__main__':
    newport=ComSerial("COM1")
    newport.write(b'send a')
    str = list(range(8))
    value = 2
    str[0] = 0x33
    str[1] = 0x01
    str[2] = 0x15
    str[3] = 0x00
    str[4] = 0xFF^(0x01 << (value-1))
    str[5] = 0x00
    str[6] = 0x08
    str[7] = 0x00
    for i in range(7):
        str[7] += str[i]
    str[7] &= 0x000000FF
    print('0x%x'%str[7])
    strs = bytes(str)
    newport.write(strs)



