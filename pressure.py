import logging.config
import time

import serialsetting
import re
import logger
import logging
import json
from log_exception import PressureSourceError

# 日志原则：报错将直接抛异常，交给上层处理；非报错将只记录debug日志信息
pressureLogger = logging.getLogger("pressure")
rotatingFileHandler = logging.handlers.RotatingFileHandler("logs/pressure.log", maxBytes=1024 * 1024, backupCount=100, encoding='utf-8')
# [%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s, encoding='utf-8'
formatter = logging.Formatter("[%(levelname)s|%(module)s|L%(lineno)d] %(asctime)s: %(message)s")
rotatingFileHandler.setFormatter(formatter)
pressureLogger.addHandler(rotatingFileHandler)
pressureLogger.setLevel(logging.DEBUG)

class PressureControl:
    
    def __init__(self, port, baudrate=9600):
        self.serial = serialsetting.ComSerial(port, baudrate=baudrate, timeout=1)
        # readline = self.serial.write_and_read_line(b':INST:MODE 1\n', 0.3)
        # print(readline)
        # if readline.replace(b'\r\n', b'') != b':INST:MODE CONT':
        # #if readline != ":INST:MODE CONT":
        #     raise ValueError('压力单元设置错误')
        # readline = self.serial.write_and_read_line(b':INST:UNIT 0\n', 0.3)
        # # print(readline)
        # if readline.replace(b'\r\n', b'') != b':INST:UNIT kPa':
        #     raise ValueError('压力值设置错误')

    def setpressure(self, value):
        setstr = ":SENS:PRES " + str(value) + "\n"
        readline = self.serial.write_and_read_line(setstr.encode("utf-8"), 0.3)
        # print(readline)
        if not (b'OK' in readline or b':SENS:PRES' in readline):
            raise PressureSourceError('压力值设置错误')

    def getpressure(self):
        readline = self.serial.write_and_read_line(b':SENS:PRES?\n', 0.3)
        #if readline != ":INST:PRES 'SET OK'":
        #    print("set pressure value error！\n")
        # print(readline)
        pattern = re.compile(r'(?<=:SENS:PRESS )\d+\.?\d*')
        return float(pattern.findall(readline.decode())[0])

    def set_still_pressure(self, value):
        setstr = ":SENS:PRES " + str(value) + "\n"
        readline = self.serial.write_and_read_line(setstr.encode("utf-8"), 0.3)
        if not (b'OK' in readline or b':SENS:PRES' in readline):
            raise PressureSourceError('压力值设置错误') # 是否应该终止?
        state_flag = 0
        state_count = 0
        t_start = time.time()
        while state_flag == 0:
            readline = self.serial.write_and_read_line(b':SENS:PRES?\n', 0.3)
            pressureLogger.debug("set pressure readline is %s", readline)
            pattern = re.compile(r'(?<=kPa )\d+\.?\d*')
            state = int(pattern.findall(readline.decode())[0])
            pressureLogger.debug("set pressure state is %d", state)
            if state: #多次异常，报错
                state_count = state_count + 1
            if state_count != 0 and state == 0: #已经成功，但又不稳定，重新计数
                state_count = 0
            if state_count == 3:
                state_flag = 1
            t_end = time.time()
            if t_end - t_start > 120:
                raise PressureSourceError('压力值稳定超时')
                return
            time.sleep(0.5)

    def vent(self):
        readline = self.serial.write_and_read_line(b':INST:MODE 2\n', 0.3)
        # print(readline)
        if readline.replace(b'\r\n', b'') != b':INST:MODE VENT':
            # if readline != ":INST:MODE CONT":
            raise PressureSourceError('压力源vent模式设置错误')
            print("set pressure vent mode error！\n")

    def cont(self):
        readline = self.serial.write_and_read_line(b':INST:MODE 1\n', 0.3)
        # print(readline)
        if readline.replace(b'\r\n', b'') != b':INST:MODE CONT':
            # if readline != ":INST:MODE CONT":
            raise PressureSourceError('压力源cont模式设置错误')
            print("set pressure vent mode error！\n")

if __name__ == '__main__':
    press = PressureControl("COM11", 9600)
    # press.setpressure(20)
    # time.sleep(0.5)
    # press_set = press.getpressure()
    # print("press is ", press_set)
    press.set_still_pressure(30)
    pressureLogger.debug("set 30 kPa is ok")
    press.vent()
    # time.sleep(10)
    # press.set_still_pressure(0)
    # print("set 0 kPa is ok")

